import 'package:flutter_test/flutter_test.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:equalcash/core/vm/create_account_vm.dart';

void main() {
  group('CreateAccountVm Country Selection Tests', () {
    late CreateAccountVm viewModel;

    setUp(() {
      viewModel = CreateAccountVm();
    });

    tearDown(() {
      viewModel.dispose();
    });

    test('should have default country as Nigeria', () {
      expect(viewModel.selectedCountry.code, 'NG');
      expect(viewModel.selectedCountry.name, 'Nigeria');
      expect(viewModel.selectedCountry.dialCode, '+234');
    });

    test('should update selectedCountry when setSelectedCountry is called', () {
      // Arrange
      const newCountry = CountryCode(code: 'GH', name: 'Ghana', dialCode: '+233');
      
      // Act
      viewModel.setSelectedCountry(newCountry);
      
      // Assert
      expect(viewModel.selectedCountry.code, 'GH');
      expect(viewModel.selectedCountry.name, 'Ghana');
      expect(viewModel.selectedCountry.dialCode, '+233');
    });

    test('should update dialCodeC controller when country is changed', () {
      // Arrange
      const newCountry = CountryCode(code: 'KE', name: 'Kenya', dialCode: '+254');
      
      // Act
      viewModel.setSelectedCountry(newCountry);
      
      // Assert
      expect(viewModel.dialCodeC.text, '+254');
    });

    test('should not change selectedCountry when null is passed', () {
      // Arrange
      const originalCountry = CountryCode(code: 'NG', name: 'Nigeria', dialCode: '+234');
      
      // Act
      viewModel.setSelectedCountry(null);
      
      // Assert
      expect(viewModel.selectedCountry.code, originalCountry.code);
      expect(viewModel.selectedCountry.name, originalCountry.name);
      expect(viewModel.selectedCountry.dialCode, originalCountry.dialCode);
    });

    test('should update to different countries correctly', () {
      // Test multiple country changes
      const countries = [
        CountryCode(code: 'GH', name: 'Ghana', dialCode: '+233'),
        CountryCode(code: 'KE', name: 'Kenya', dialCode: '+254'),
        CountryCode(code: 'UG', name: 'Uganda', dialCode: '+256'),
      ];

      for (final country in countries) {
        viewModel.setSelectedCountry(country);
        
        expect(viewModel.selectedCountry.code, country.code);
        expect(viewModel.selectedCountry.name, country.name);
        expect(viewModel.selectedCountry.dialCode, country.dialCode);
        expect(viewModel.dialCodeC.text, country.dialCode);
      }
    });
  });
}
