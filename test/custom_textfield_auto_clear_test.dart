import 'package:equalcash/ui/components/shared/textfields/custom_textfield.dart';
import 'package:equalcash/ui/components/shared/textfields/validation/validation_rule.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CustomTextField Auto Validation Clear Tests', () {
    late TextEditingController controller;

    setUp(() {
      controller = TextEditingController();
    });

    tearDown(() {
      controller.dispose();
    });

    testWidgets(
        'should clear validation error when text is set programmatically',
        (WidgetTester tester) async {
      // Build the widget with validation rules
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          builder: (context, child) => MaterialApp(
            home: Scaffold(
              body: CustomTextField(
                controller: controller,
                labelText: 'Test Field',
                validationRules: const [
                  RequiredRule(fieldName: 'test field'),
                ],
                enableRealTimeValidation: true,
              ),
            ),
          ),
        ),
      );

      // Programmatically set text (this should work without exceptions)
      controller.text = 'Valid text';
      await tester.pumpAndSettle();

      // Verify that the text was set
      expect(controller.text, equals('Valid text'));

      // Verify that setting text programmatically doesn't cause any exceptions
      expect(tester.takeException(), isNull);

      // Clear the text programmatically
      controller.text = '';
      await tester.pumpAndSettle();

      // Verify that clearing text programmatically doesn't cause any exceptions
      expect(tester.takeException(), isNull);
    });

    testWidgets('should handle user typing without exceptions',
        (WidgetTester tester) async {
      // Build the widget with validation rules
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          builder: (context, child) => MaterialApp(
            home: Scaffold(
              body: CustomTextField(
                controller: controller,
                labelText: 'Test Field',
                validationRules: const [
                  RequiredRule(fieldName: 'test field'),
                ],
                enableRealTimeValidation: true,
              ),
            ),
          ),
        ),
      );

      // Find the text field
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);

      // Type in the field
      await tester.enterText(textField, 'Valid text');
      await tester.pumpAndSettle();

      // Verify that typing doesn't cause any exceptions
      expect(tester.takeException(), isNull);
      expect(controller.text, equals('Valid text'));
    });

    testWidgets('should handle empty text programmatically',
        (WidgetTester tester) async {
      // Build the widget with validation rules
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          builder: (context, child) => MaterialApp(
            home: Scaffold(
              body: CustomTextField(
                controller: controller,
                labelText: 'Test Field',
                validationRules: const [
                  RequiredRule(fieldName: 'test field'),
                ],
                enableRealTimeValidation: true,
              ),
            ),
          ),
        ),
      );

      // Set some text first
      controller.text = 'Some text';
      await tester.pumpAndSettle();

      // Clear the text programmatically
      controller.text = '';
      await tester.pumpAndSettle();

      // Should not show error immediately when cleared programmatically
      expect(find.text('Please enter a valid test field'), findsNothing);
    });

    testWidgets('should work without validation rules',
        (WidgetTester tester) async {
      // Build the widget without validation rules
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          builder: (context, child) => MaterialApp(
            home: Scaffold(
              body: CustomTextField(
                controller: controller,
                labelText: 'Test Field',
                // No validation rules
              ),
            ),
          ),
        ),
      );

      // Should not crash when setting text programmatically
      controller.text = 'Some text';
      await tester.pumpAndSettle();

      // Should work fine
      expect(find.text('Some text'), findsOneWidget);
    });
  });
}
