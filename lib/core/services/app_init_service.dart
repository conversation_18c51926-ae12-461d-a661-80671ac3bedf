// ignore_for_file: use_build_context_synchronously

import 'package:equalcash/core/core.dart';
import 'package:equalcash/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppInitService {
  init() async {
    //screen orientation
    await _screenOrientationInit();

    // firebase
    await _firebaseInit();

    // Dotenv
    await dotenv.load(fileName: ".env");
    AppConfig.setEnvironmentType(EnvironmentType.staging);

    //hive
    _initializeHive();

    //fcm
    // FirebasePushNotificationService.init(fcmBackgroundHandler);

    // Sec Init
    await secondaryInit();
  }

  secondaryInit() async {
    printty("secondaryInit======");
    // remote config
    // await RemoteConfigService().init();

    //push notification
    // LocalPushNotificationService.init();

    //package info
    // _packageInfoInit();

    //header info service
    // await HeaderService().getDeviceInfo();

    // AppsflyerService
    // AppsflyService.init();
  }

  Future<String> packageInfoInit() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      await StorageService.storeStringItem(
          StorageKey.appVersion, packageInfo.version);

      printty("===> package info initialized... ${packageInfo.version}");

      return packageInfo.version;
    } catch (e) {
      printty(e.toString(), logName: 'PackageInfo Error check');
      return "";
    }
  }

  _firebaseInit() async {
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      printty("===> firebase initialized...");
    } catch (e) {
      printty(e.toString(), logName: 'Firebase Error check');
    }
  }

  void _initializeHive() async {
    try {
      await Hive.initFlutter();
      await Hive.openBox(StorageKey.generalHiveBox);
      printty("===> hive initialized...");
    } catch (e) {
      printty(e.toString(), logName: 'Hive Error check');
    }
  }

  // _crashlyticsInit() async {
  //   FlutterError.onError = (errorDetails) {
  //     FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  //   };
  //   // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  //   PlatformDispatcher.instance.onError = (error, stack) {
  //     FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
  //     return true;
  //   };
  //   printty("===> crashlytics initialized...");
  // }

  _screenOrientationInit() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    printty("===> screen orientation initialized...");
  }
}
