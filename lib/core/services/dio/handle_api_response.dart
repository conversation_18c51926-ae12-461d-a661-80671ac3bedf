import 'package:equalcash/core/core.dart';

void handleApiResponse({
  required ApiResponse response,
  int? duration,
  bool showToast = true,
  void Function()? onCompleted,
  void Function()? onError,
}) {
  if (response.success) {
    if (onCompleted != null) onCompleted();
    if (showToast) {
      FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.success,
          duration: duration,
          message: response.message ?? 'Operation successful');
    }
  } else {
    if (onError != null) onError();
    FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        duration: duration,
        message: response.message ?? 'Something went wrong');
  }
}
