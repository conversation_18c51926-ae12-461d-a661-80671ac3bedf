class PwdResetArg {
  PwdResetArg({
    required this.recipent,
    this.code,
    required this.password,
    required this.passwordConfirmation,
    this.currentPassword,
  });

  final String recipent;
  final String? code;
  final String password;
  final String passwordConfirmation;
  final String? currentPassword;

  Map<String, dynamic> toMap() {
    return {
      'recipent': recipent,
      'code': code,
      'password': password,
      'password_confirmation': passwordConfirmation,
      'current_password': currentPassword,
    };
  }
}
