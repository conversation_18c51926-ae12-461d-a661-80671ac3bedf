class ProfileParams {
  final String? occupation;
  final String? dateOfBirth;
  final String? gender;
  final String? address;
  final int? kycStep;

  ProfileParams({
    this.occupation,
    this.dateOfBirth,
    this.gender,
    this.address,
    this.kycStep,
  });

  Map<String, dynamic> toMap() {
    return {
      'occupation': occupation,
      'date_of_birth': dateOfBirth,
      'gender': gender,
      'address': address,
      'kyc_step': kycStep,
    };
  }
}
