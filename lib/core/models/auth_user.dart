import 'dart:convert';

AuthUser authUserFromJson(String str) => AuthUser.fromJson(json.decode(str));

String authUserToJson(AuthUser data) => json.encode(data.toJson());

class AuthUser {
  final int? id;
  final String? firstName;
  final dynamic middleName;
  final String? lastName;
  final String? userName;
  final String? fullName;
  final String? email;
  final DateTime? emailVerifiedAt;
  final String? phone;
  final DateTime? phoneVerifiedAt;
  final DateTime? loggedInAt;
  final dynamic loggedOutAt;
  final dynamic passwordChangedAt;
  final String? status;
  final bool? isSuspended;
  final dynamic suspendedReason;
  final bool? isDeactivated;
  final dynamic deactivatedReason;
  final int? kycStep;
  final DateTime? dateOfBirth;
  final dynamic profileImageUrl;
  final String? gender;
  final String? occupation;
  final String? address;
  final String? country;
  final bool? hasTransactionPin;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  AuthUser({
    this.id,
    this.firstName,
    this.middleName,
    this.lastName,
    this.userName,
    this.fullName,
    this.email,
    this.emailVerifiedAt,
    this.phone,
    this.phoneVerifiedAt,
    this.loggedInAt,
    this.loggedOutAt,
    this.passwordChangedAt,
    this.status,
    this.isSuspended,
    this.suspendedReason,
    this.isDeactivated,
    this.deactivatedReason,
    this.kycStep,
    this.dateOfBirth,
    this.profileImageUrl,
    this.gender,
    this.occupation,
    this.address,
    this.country,
    this.hasTransactionPin,
    this.createdAt,
    this.updatedAt,
  });

  AuthUser copyWith({
    int? id,
    String? firstName,
    dynamic middleName,
    String? lastName,
    String? userName,
    String? fullName,
    String? email,
    DateTime? emailVerifiedAt,
    String? phone,
    DateTime? phoneVerifiedAt,
    DateTime? loggedInAt,
    dynamic loggedOutAt,
    dynamic passwordChangedAt,
    String? status,
    bool? isSuspended,
    dynamic suspendedReason,
    bool? isDeactivated,
    dynamic deactivatedReason,
    int? kycStep,
    DateTime? dateOfBirth,
    dynamic profileImageUrl,
    String? gender,
    String? occupation,
    String? address,
    String? country,
    bool? hasTransactionPin,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      AuthUser(
        id: id ?? this.id,
        firstName: firstName ?? this.firstName,
        middleName: middleName ?? this.middleName,
        lastName: lastName ?? this.lastName,
        userName: userName ?? this.userName,
        fullName: fullName ?? this.fullName,
        email: email ?? this.email,
        emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
        phone: phone ?? this.phone,
        phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
        loggedInAt: loggedInAt ?? this.loggedInAt,
        loggedOutAt: loggedOutAt ?? this.loggedOutAt,
        passwordChangedAt: passwordChangedAt ?? this.passwordChangedAt,
        status: status ?? this.status,
        isSuspended: isSuspended ?? this.isSuspended,
        suspendedReason: suspendedReason ?? this.suspendedReason,
        isDeactivated: isDeactivated ?? this.isDeactivated,
        deactivatedReason: deactivatedReason ?? this.deactivatedReason,
        kycStep: kycStep ?? this.kycStep,
        dateOfBirth: dateOfBirth ?? this.dateOfBirth,
        profileImageUrl: profileImageUrl ?? this.profileImageUrl,
        gender: gender ?? this.gender,
        occupation: occupation ?? this.occupation,
        address: address ?? this.address,
        country: country ?? this.country,
        hasTransactionPin: hasTransactionPin ?? this.hasTransactionPin,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory AuthUser.fromJson(Map<String, dynamic> json) => AuthUser(
        id: json["id"],
        firstName: json["first_name"],
        middleName: json["middle_name"],
        lastName: json["last_name"],
        userName: json["user_name"],
        fullName: json["full_name"],
        email: json["email"],
        emailVerifiedAt: json["email_verified_at"] == null
            ? null
            : DateTime.parse(json["email_verified_at"]),
        phone: json["phone"],
        phoneVerifiedAt: json["phone_verified_at"] == null
            ? null
            : DateTime.parse(json["phone_verified_at"]),
        loggedInAt: json["logged_in_at"] == null
            ? null
            : DateTime.parse(json["logged_in_at"]),
        loggedOutAt: json["logged_out_at"],
        passwordChangedAt: json["password_changed_at"],
        status: json["status"],
        isSuspended: json["is_suspended"],
        suspendedReason: json["suspended_reason"],
        isDeactivated: json["is_deactivated"],
        deactivatedReason: json["deactivated_reason"],
        kycStep: json["kyc_step"],
        dateOfBirth: json["date_of_birth"] == null
            ? null
            : DateTime.parse(json["date_of_birth"]),
        profileImageUrl: json["profile_image_url"],
        gender: json["gender"],
        occupation: json["occupation"],
        address: json["address"],
        country: json["country"],
        hasTransactionPin: json["has_transaction_pin"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "first_name": firstName,
        "middle_name": middleName,
        "last_name": lastName,
        "user_name": userName,
        "full_name": fullName,
        "email": email,
        "email_verified_at": emailVerifiedAt?.toIso8601String(),
        "phone": phone,
        "phone_verified_at": phoneVerifiedAt?.toIso8601String(),
        "logged_in_at": loggedInAt?.toIso8601String(),
        "logged_out_at": loggedOutAt,
        "password_changed_at": passwordChangedAt,
        "status": status,
        "is_suspended": isSuspended,
        "suspended_reason": suspendedReason,
        "is_deactivated": isDeactivated,
        "deactivated_reason": deactivatedReason,
        "kyc_step": kycStep,
        "date_of_birth":
            "${dateOfBirth!.year.toString().padLeft(4, '0')}-${dateOfBirth!.month.toString().padLeft(2, '0')}-${dateOfBirth!.day.toString().padLeft(2, '0')}",
        "profile_image_url": profileImageUrl,
        "gender": gender,
        "occupation": occupation,
        "address": address,
        "country": country,
        "has_transaction_pin": hasTransactionPin,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
