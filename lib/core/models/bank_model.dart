import 'dart:convert';

List<BankModel> bankModelFromJson(String str) =>
    List<BankModel>.from(json.decode(str).map((x) => BankModel.fromJson(x)));

String bankModelToJson(List<BankModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BankModel {
  final int? id;
  final String? name;
  final String? slug;
  final String? code;
  final String? longcode;
  final String? gateway;
  final bool? payWithBank;
  final bool? supportsTransfer;
  final bool? active;
  final String? country;
  final String? currency;
  final String? type;
  final bool? isDeleted;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  BankModel({
    this.id,
    this.name,
    this.slug,
    this.code,
    this.longcode,
    this.gateway,
    this.payWithBank,
    this.supportsTransfer,
    this.active,
    this.country,
    this.currency,
    this.type,
    this.isDeleted,
    this.createdAt,
    this.updatedAt,
  });

  factory BankModel.fromJson(Map<String, dynamic> json) => BankModel(
        id: json["id"],
        name: json["name"],
        slug: json["slug"],
        code: json["code"],
        longcode: json["longcode"],
        gateway: json["gateway"],
        payWithBank: json["pay_with_bank"],
        supportsTransfer: json["supports_transfer"],
        active: json["active"],
        country: json["country"],
        currency: json["currency"],
        type: json["type"],
        isDeleted: json["is_deleted"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "slug": slug,
        "code": code,
        "longcode": longcode,
        "gateway": gateway,
        "pay_with_bank": payWithBank,
        "supports_transfer": supportsTransfer,
        "active": active,
        "country": country,
        "currency": currency,
        "type": type,
        "is_deleted": isDeleted,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
      };
}
