import 'dart:convert';

List<BankAccountModel> bankAccountModelFromJson(String str) =>
    List<BankAccountModel>.from(
        json.decode(str).map((x) => BankAccountModel.fromJson(x)));

String bankAccountModelToJson(List<BankAccountModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BankAccountModel {
  final int? id;
  final String? accountNumber;
  final String? accountName;
  final String? bankCode;
  final String? bankName;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  BankAccountModel({
    this.id,
    this.accountNumber,
    this.accountName,
    this.bankCode,
    this.bankName,
    this.createdAt,
    this.updatedAt,
  });

  factory BankAccountModel.fromJson(Map<String, dynamic> json) =>
      BankAccountModel(
        id: json["id"],
        accountNumber: json["account_number"],
        accountName: json["account_name"],
        bankCode: json["bank_code"],
        bankName: json["bank_name"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "account_number": accountNumber,
        "account_name": accountName,
        "bank_code": bankCode,
        "bank_name": bankName,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
