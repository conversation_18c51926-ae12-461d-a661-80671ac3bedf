import 'dart:convert';

import 'package:equalcash/core/models/member_model.dart';

List<CompanyModel> companyModelFromJson(String str) => List<CompanyModel>.from(
    json.decode(str).map((x) => CompanyModel.fromJson(x)));

class CompanyModel {
  final int? id;
  final String? name;
  final String? cashBucket;
  final String? cashDrop;
  final String? cashReserveRatio;
  final int? duration;
  final int? numberOfStakeholders;
  final DateTime? commencementDate;
  final DateTime? completionDate;
  final String? type;
  final bool? collectionOrderTradeable;
  final String? status;
  final MemberModel? authMember;
  final Ceo? ceo;
  final List<MemberModel>? members;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  CompanyModel({
    this.id,
    this.name,
    this.cashBucket,
    this.cashDrop,
    this.cashReserveRatio,
    this.duration,
    this.numberOfStakeholders,
    this.commencementDate,
    this.completionDate,
    this.type,
    this.collectionOrderTradeable,
    this.status,
    this.authMember,
    this.ceo,
    this.members,
    this.createdAt,
    this.updatedAt,
  });

  factory CompanyModel.fromJson(Map<String, dynamic> json) => CompanyModel(
        id: json["id"],
        name: json["name"],
        cashBucket: json["cash_bucket"],
        cashDrop: json["cash_drop"],
        cashReserveRatio: json["cash_reserve_ratio"],
        duration: json["duration"],
        numberOfStakeholders: json["number_of_stakeholders"],
        commencementDate: json["commencement_date"] == null
            ? null
            : DateTime.parse(json["commencement_date"]),
        completionDate: json["completion_date"] == null
            ? null
            : DateTime.parse(json["completion_date"]),
        type: json["type"],
        collectionOrderTradeable: json["collection_order_tradeable"],
        status: json["status"],
        authMember: json["auth_member"] == null
            ? null
            : MemberModel.fromJson(json["auth_member"]),
        ceo: json["ceo"] == null ? null : Ceo.fromJson(json["ceo"]),
        members: json["members"] == null
            ? []
            : List<MemberModel>.from(
                json["members"]!.map((x) => MemberModel.fromJson(x))),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "cash_bucket": cashBucket,
        "cash_drop": cashDrop,
        "cash_reserve_ratio": cashReserveRatio,
        "duration": duration,
        "number_of_stakeholders": numberOfStakeholders,
        "commencement_date": commencementDate?.toIso8601String(),
        "completion_date": completionDate?.toIso8601String(),
        "type": type,
        "collection_order_tradeable": collectionOrderTradeable,
        "status": status,
        "auth_member": authMember?.toJson(),
        "ceo": ceo?.toJson(),
        "members": members == null
            ? []
            : List<dynamic>.from(members!.map((x) => x.toJson())),
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class Ceo {
  final int? id;
  final String? firstName;
  final dynamic middleName;
  final String? lastName;
  final String? userName;
  final String? fullName;
  final String? email;
  final dynamic emailVerifiedAt;
  final String? phone;
  final DateTime? phoneVerifiedAt;
  final DateTime? loggedInAt;
  final dynamic loggedOutAt;
  final dynamic passwordChangedAt;
  final String? status;
  final bool? isSuspended;
  final dynamic suspendedReason;
  final bool? isDeactivated;
  final dynamic deactivatedReason;
  final int? kycStep;
  final DateTime? dateOfBirth;
  final dynamic profileImageUrl;
  final String? gender;
  final String? occupation;
  final String? address;
  final String? country;
  final bool? hasTransactionPin;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Ceo({
    this.id,
    this.firstName,
    this.middleName,
    this.lastName,
    this.userName,
    this.fullName,
    this.email,
    this.emailVerifiedAt,
    this.phone,
    this.phoneVerifiedAt,
    this.loggedInAt,
    this.loggedOutAt,
    this.passwordChangedAt,
    this.status,
    this.isSuspended,
    this.suspendedReason,
    this.isDeactivated,
    this.deactivatedReason,
    this.kycStep,
    this.dateOfBirth,
    this.profileImageUrl,
    this.gender,
    this.occupation,
    this.address,
    this.country,
    this.hasTransactionPin,
    this.createdAt,
    this.updatedAt,
  });

  factory Ceo.fromJson(Map<String, dynamic> json) => Ceo(
        id: json["id"],
        firstName: json["first_name"],
        middleName: json["middle_name"],
        lastName: json["last_name"],
        userName: json["user_name"],
        fullName: json["full_name"],
        email: json["email"],
        emailVerifiedAt: json["email_verified_at"],
        phone: json["phone"],
        phoneVerifiedAt: json["phone_verified_at"] == null
            ? null
            : DateTime.parse(json["phone_verified_at"]),
        loggedInAt: json["logged_in_at"] == null
            ? null
            : DateTime.parse(json["logged_in_at"]),
        loggedOutAt: json["logged_out_at"],
        passwordChangedAt: json["password_changed_at"],
        status: json["status"],
        isSuspended: json["is_suspended"],
        suspendedReason: json["suspended_reason"],
        isDeactivated: json["is_deactivated"],
        deactivatedReason: json["deactivated_reason"],
        kycStep: json["kyc_step"],
        dateOfBirth: json["date_of_birth"] == null
            ? null
            : DateTime.parse(json["date_of_birth"]),
        profileImageUrl: json["profile_image_url"],
        gender: json["gender"],
        occupation: json["occupation"],
        address: json["address"],
        country: json["country"],
        hasTransactionPin: json["has_transaction_pin"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "first_name": firstName,
        "middle_name": middleName,
        "last_name": lastName,
        "user_name": userName,
        "full_name": fullName,
        "email": email,
        "email_verified_at": emailVerifiedAt,
        "phone": phone,
        "phone_verified_at": phoneVerifiedAt?.toIso8601String(),
        "logged_in_at": loggedInAt?.toIso8601String(),
        "logged_out_at": loggedOutAt,
        "password_changed_at": passwordChangedAt,
        "status": status,
        "is_suspended": isSuspended,
        "suspended_reason": suspendedReason,
        "is_deactivated": isDeactivated,
        "deactivated_reason": deactivatedReason,
        "kyc_step": kycStep,
        "date_of_birth":
            "${dateOfBirth!.year.toString().padLeft(4, '0')}-${dateOfBirth!.month.toString().padLeft(2, '0')}-${dateOfBirth!.day.toString().padLeft(2, '0')}",
        "profile_image_url": profileImageUrl,
        "gender": gender,
        "occupation": occupation,
        "address": address,
        "country": country,
        "has_transaction_pin": hasTransactionPin,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
