import 'dart:convert';

ConfigModel configModelFromJson(String str) =>
    ConfigModel.fromJson(json.decode(str));

String configModelToJson(ConfigModel data) => json.encode(data.toJson());

class ConfigModel {
  final String? referralBonusAmount;
  final String? transactionAmountForReferralBonus;
  final String? phoneNumber;
  final String? emailAddress;
  final String? facebookUrl;
  final String? twitterUrl;
  final String? instagramUrl;
  final String? linkedinUrl;
  final String? appStoreUrl;
  final String? playStoreUrl;

  ConfigModel({
    this.referralBonusAmount,
    this.transactionAmountForReferralBonus,
    this.phoneNumber,
    this.emailAddress,
    this.facebookUrl,
    this.twitterUrl,
    this.instagramUrl,
    this.linkedinUrl,
    this.appStoreUrl,
    this.playStoreUrl,
  });

  factory ConfigModel.fromJson(Map<String, dynamic> json) => ConfigModel(
        referralBonusAmount: json["referral_bonus_amount"],
        transactionAmountForReferralBonus:
            json["transaction_amount_for_referral_bonus"],
        phoneNumber: json["phone_number"],
        emailAddress: json["email_address"],
        facebookUrl: json["facebook_url"],
        twitterUrl: json["twitter_url"],
        instagramUrl: json["instagram_url"],
        linkedinUrl: json["linkedin_url"],
        appStoreUrl: json["app_store_url"],
        playStoreUrl: json["play_store_url"],
      );

  Map<String, dynamic> toJson() => {
        "referral_bonus_amount": referralBonusAmount,
        "transaction_amount_for_referral_bonus":
            transactionAmountForReferralBonus,
        "phone_number": phoneNumber,
        "email_address": emailAddress,
        "facebook_url": facebookUrl,
        "twitter_url": twitterUrl,
        "instagram_url": instagramUrl,
        "linkedin_url": linkedinUrl,
        "app_store_url": appStoreUrl,
        "play_store_url": playStoreUrl,
      };
}
