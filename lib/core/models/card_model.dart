import 'dart:convert';

List<CardModel> cardModelFromJson(String str) =>
    List<CardModel>.from(json.decode(str).map((x) => CardModel.fromJson(x)));

String cardModelToJson(List<CardModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CardModel {
  final int? id;
  final String? authorizationCode;
  final String? signature;
  final String? cardType;
  final String? last4;
  final String? email;
  final String? expiryMonth;
  final String? expiryYear;
  final String? bin;
  final String? bank;
  final String? channel;
  final String? reusable;
  final String? countryCode;
  final dynamic accountName;
  final bool? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  CardModel({
    this.id,
    this.authorizationCode,
    this.signature,
    this.cardType,
    this.last4,
    this.email,
    this.expiryMonth,
    this.expiryYear,
    this.bin,
    this.bank,
    this.channel,
    this.reusable,
    this.countryCode,
    this.accountName,
    this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  factory CardModel.fromJson(Map<String, dynamic> json) => CardModel(
        id: json["id"],
        authorizationCode: json["authorization_code"],
        signature: json["signature"],
        cardType: json["card_type"],
        last4: json["last4"],
        email: json["email"],
        expiryMonth: json["expiry_month"],
        expiryYear: json["expiry_year"],
        bin: json["bin"],
        bank: json["bank"],
        channel: json["channel"],
        reusable: json["reusable"],
        countryCode: json["country_code"],
        accountName: json["account_name"],
        isActive: json["is_active"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "authorization_code": authorizationCode,
        "signature": signature,
        "card_type": cardType,
        "last4": last4,
        "email": email,
        "expiry_month": expiryMonth,
        "expiry_year": expiryYear,
        "bin": bin,
        "bank": bank,
        "channel": channel,
        "reusable": reusable,
        "country_code": countryCode,
        "account_name": accountName,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
