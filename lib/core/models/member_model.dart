import 'dart:convert';

List<MemberModel> memberModelFromJson(String str) => List<MemberModel>.from(
    json.decode(str).map((x) => MemberModel.fromJson(x)));

class MemberModel {
  final int? id;
  final String? invitationIdentifier;
  final bool? isCeo;
  final int? collectionOrder;
  final int? tradingCount;
  final String? invitationType;
  final String? invitationStatus;
  final bool? isNextForCollection;
  final DateTime? collectionDate;
  final dynamic collectionStatus;
  final dynamic collectionOrderAgreed;
  final bool? termsAgreed;
  final String? firstName;
  final String? lastName;
  final int? userId;
  final String? userName;
  final String? email;
  final String? phone;
  final DateTime? createdAt;

  MemberModel({
    this.id,
    this.invitationIdentifier,
    this.isCeo,
    this.collectionOrder,
    this.tradingCount,
    this.invitationType,
    this.invitationStatus,
    this.isNextForCollection,
    this.collectionDate,
    this.collectionStatus,
    this.collectionOrderAgreed,
    this.termsAgreed,
    this.firstName,
    this.lastName,
    this.userId,
    this.userName,
    this.email,
    this.phone,
    this.createdAt,
  });

  factory MemberModel.fromJson(Map<String, dynamic> json) => MemberModel(
        id: json["id"],
        invitationIdentifier: json["invitation_identifier"],
        isCeo: json["is_ceo"],
        collectionOrder: json["collection_order"],
        tradingCount: json["trading_count"],
        invitationType: json["invitation_type"],
        invitationStatus: json["invitation_status"],
        isNextForCollection: json["is_next_for_collection"],
        collectionDate: json["collection_date"] == null
            ? null
            : DateTime.parse(json["collection_date"]),
        collectionStatus: json["collection_status"],
        collectionOrderAgreed: json["collection_order_agreed"],
        termsAgreed: json["terms_agreed"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        userId: json["user_id"],
        userName: json["user_name"],
        email: json["email"],
        phone: json["phone"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "invitation_identifier": invitationIdentifier,
        "is_ceo": isCeo,
        "collection_order": collectionOrder,
        "trading_count": tradingCount,
        "invitation_type": invitationType,
        "invitation_status": invitationStatus,
        "is_next_for_collection": isNextForCollection,
        "collection_date": collectionDate?.toIso8601String(),
        "collection_status": collectionStatus,
        "collection_order_agreed": collectionOrderAgreed,
        "terms_agreed": termsAgreed,
        "first_name": firstName,
        "last_name": lastName,
        "user_id": userId,
        "user_name": userName,
        "email": email,
        "phone": phone,
        "created_at": createdAt?.toIso8601String(),
      };
}
