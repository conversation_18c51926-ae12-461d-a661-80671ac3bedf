extension StringExtension on String {
  String capitalizeFirstLetter() {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }
}

// String capitalizeFirstLetter(String text) {
//   if (text.isEmpty) return text;
//   return text[0].toUpperCase() + text.substring(1);
// }

extension StringExt on String {
  String replaceAllCommas() {
    return replaceAll(',', '');
  }
}
