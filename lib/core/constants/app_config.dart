import 'package:equalcash/core/enums/environment_type.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConfig {
  static late Map<String, dynamic> _config;

  static void setEnvironmentType(EnvironmentType env) {
    switch (env) {
      case EnvironmentType.dev:
      case EnvironmentType.qa:
        _config = _BaseUrlConfig.devConstants;
        break;
      case EnvironmentType.staging:
        _config = _BaseUrlConfig.stagingConstants;
        break;
      case EnvironmentType.prod:
        _config = _BaseUrlConfig.prodConstants;
        break;
    }
  }

  static get baseUrl {
    return _config[_BaseUrlConfig.baseUrl];
  }

  static get payStackKey {
    return _config[_BaseUrlConfig.payStackKey];
  }
}

class _BaseUrlConfig {
  static const baseUrl = 'BaseUrl';
  static const payStackKey = 'PayStackPublicKey';

  static Map<String, dynamic> devConstants = {
    baseUrl: dotenv.env['DEV_URL'],
    payStackKey: dotenv.env['DEBUG_PAY_STACK_KEY'],
  };

  static Map<String, dynamic> stagingConstants = {
    baseUrl: dotenv.env['STAGE_URL'],
    payStackKey: dotenv.env['DEBUG_PAY_STACK_KEY'],
  };

  static Map<String, dynamic> prodConstants = {
    baseUrl: dotenv.env['PROD_URL'],
    payStackKey: dotenv.env['PROD_PAY_STACK_KEY'],
  };
}
