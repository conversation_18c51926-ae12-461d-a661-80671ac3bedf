import 'package:equalcash/core/core.dart';

class CardVm extends BaseVm {
  List<CardModel> _cards = [];
  List<CardModel> get cards => _cards;

  Future<ApiResponse> initializeCardTransaction() async {
    return await performApiCall(
      url: "/v1/cards/initialize-card-transaction",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        return ApiResponse(
            success: true, data: data?["data"]?["authorization_url"]);
      },
    );
  }

  Future<ApiResponse> toggleCardStatus(int cardId) async {
    return await performApiCall(
      url: "/v1/cards/toggle-card-status/$cardId",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getCards() async {
    return await performApiCall(
      url: "/v1/cards",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _cards = cardModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}

final cardVmodel = ChangeNotifierProvider<CardVm>((ref) {
  return CardVm();
});
