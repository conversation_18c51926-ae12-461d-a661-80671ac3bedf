import 'package:equalcash/core/core.dart';

class LoginVm extends BaseVm {
  Future<ApiResponse> login({
    required String userName,
    required String pwd,
  }) async {
    return await performApiCall(
      url: "/v1/auth/login",
      method: apiService.post,
      body: {
        "user_name": userName,
        "password": pwd,
      },
      onSuccess: (data) {
        String token = data["data"]["token"];
        StorageService.storeAccessToken(token);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> logout() async {
    try {
      setBusy(true);
      await Future.delayed(const Duration(seconds: 1));
      final AuthUser? user = await StorageService.getUser();
      Navigator.pushNamedAndRemoveUntil(
        NavKey.appNavigatorKey.currentContext!,
        RoutePath.passwordLoginScreen,
        (r) => false,
      );
      setBusy(false);
      String url = "/v1/auth/logout";
      apiResponse = await apiService.postWithAuth(body: null, url: url);

      await StorageService.logout();
      await StorageService.storeStringItem(StorageKey.email, user?.email ?? "");

      printty(apiResponse, logName: "Logout Response");
      return apiResponse;
    } catch (e) {
      printty(e.toString(), logName: "Logout Error");
      setBusy(false);
      return ApiResponse(success: false, message: e.toString());
      //return errorResponse;
    }
  }
}

final loginVm = ChangeNotifierProvider((ref) {
  return LoginVm();
});
