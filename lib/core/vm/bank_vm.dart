import 'package:equalcash/core/core.dart';

const String addState = "addState";
const String validateState = "validateState";

class BankViewModel extends BaseVm {
  List<BankModel> _allBanks = [];
  List<BankModel> get allBanks => _allBanks;
  final List<BankModel> _filteredBanks = [];
  List<BankModel> get filteredBanks => _filteredBanks;
  List<BankAccountModel> _bankAccounts = [];
  List<BankAccountModel> get bankAccounts => _bankAccounts;
  final bool _addCardLoading = false;
  bool get addCardLoading => _addCardLoading;

  Future<ApiResponse?> getAllBanks() async {
    return await performApiCall(
      url: "/v1/bank-accounts/banks",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _allBanks = bankModelFromJson(json.encode(data["data"]));
        _filteredBanks.clear();
        _filteredBanks.addAll(_allBanks);
        return apiResponse;
      },
    );
  }

  void searchBanks(String query) {
    if (query.isEmpty) {
      _filteredBanks.clear();
      _filteredBanks.addAll(_allBanks);
    } else {
      _filteredBanks.clear();
      _filteredBanks.addAll(_allBanks.where((bank) =>
          bank.name?.toLowerCase().contains(query.toLowerCase()) ?? false));
    }
    notifyListeners();
  }

  Future<ApiResponse?> getMyBankAccounts() async {
    return await performApiCall(
      url: "/v1/bank-accounts",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _bankAccounts = bankAccountModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse?> fetchBankAccount({required String id}) async {
    setBusy(true);
    return await performApiCall(
      url: "/v1/bank-accounts/$id",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        // _myBankAccounts = BankAccountResponse.fromJson(data).data;
        return apiResponse;
      },
    );
  }

  Future<ApiResponse?> addBankAccount({
    required String accountNumber,
    required String bankCode,
    required String accountName,
    required String bankName,
  }) async {
    var body = {
      "account_number": accountNumber,
      "bank_code": bankCode,
      "account_name": accountName,
      "bank_name": bankName,
    };
    return await performApiCall(
      url: "/v1/bank-accounts/add-account",
      method: apiService.postWithAuth,
      busyObjectName: addState,
      body: body,
      isFormData: false,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> verifyBankAccount({
    required String accountNumber,
    required String bankCode,
  }) async {
    var body = {
      "account_number": accountNumber,
      "bank_code": bankCode,
    };
    return await performApiCall(
      url: "/v1/bank-accounts/validate-account",
      method: apiService.postWithAuth,
      busyObjectName: validateState,
      body: body,
      onSuccess: (data) {
        return ApiResponse(success: true, data: data["data"]);
      },
    );
  }
}

final bankVmodel = ChangeNotifierProvider<BankViewModel>((ref) {
  return BankViewModel();
});
