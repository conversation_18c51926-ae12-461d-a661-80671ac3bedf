import 'package:equalcash/core/core.dart';

class ConfigVm extends BaseVm {
  ConfigModel? _configModel;
  ConfigModel? get configModel => _configModel;

  Future<ApiResponse> getConfigurations() async {
    return await performApiCall(
      url: "/v1/configurations",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _configModel = configModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}

final configVmodel = ChangeNotifierProvider<ConfigVm>((ref) {
  return ConfigVm();
});
