import 'package:equalcash/core/core.dart';

class PasswordVm extends BaseVm {
  Future<ApiResponse> pwdRequestOtp({
    required String recipient,
    VerificationType vType = VerificationType.phone,
  }) async {
    return await performApiCall(
      url: "/v1/auth/password/request-otp",
      method: apiService.post,
      body: {
        "recipient": recipient,
        "verification_type": vType.value,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }
}

final passwordVm = ChangeNotifierProvider<PasswordVm>((ref) {
  return PasswordVm();
});
