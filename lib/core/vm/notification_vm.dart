import 'package:equalcash/core/core.dart';

class NotificationVm extends BaseVm {
  List<NotificationModel> _allNotifications = [];
  List<NotificationModel?> get allNotifications => _allNotifications;

  List<NotificationModel?> get transactionNotifications =>
      _allNotifications.where((e) => e.type == 'transaction').toList();

  List<NotificationModel?> get activitiesNotifications =>
      _allNotifications.where((e) => e.type != 'transaction').toList();

  Future<ApiResponse> getAllNotifications([bool showLoader = false]) async {
    if (showLoader) {
      setBusy(true);
    }
    return await performApiCall(
      url: "/v1/notifications",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _allNotifications =
            notificationModelFromJson(json.encode(data["datatable"]["data"]));
        return apiResponse;
      },
    );
  }

  // Future<ApiResponse> getReadNotifications() async {
  //   return await performApiCall(
  //     url: "/v1/notifications/read",
  //     method: apiService.getWithAuth,
  //     onSuccess: (data) {
  //       // _authUser = authUserFromJson(json.encode(data["data"]));
  //       _readNotifications = NotificationResponse.fromJson(apiResponse.data)
  //           .datatable
  //           ?.notifications;
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> getUnreadNotifications() async {
  //   return await performApiCall(
  //     url: "/v1/notifications/read",
  //     method: apiService.getWithAuth,
  //     onSuccess: (data) {
  //       // _authUser = authUserFromJson(json.encode(data["data"]));
  //       _unreadNotifications = NotificationResponse.fromJson(apiResponse.data)
  //           .datatable
  //           ?.notifications;
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> viewNotifications(String notificationId) async {
  //   return await performApiCall(
  //     url: "/v1/notifications/:$notificationId/view",
  //     method: apiService.getWithAuth,
  //     onSuccess: (data) {
  //       // _authUser = authUserFromJson(json.encode(data["data"]));
  //       var notificationModel = ViewedNotificationResponse.fromJson(data);
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> markNotifications() async {
  //   return await performApiCall(
  //     url: "/v1/notifications/read",
  //     body: {},
  //     method: apiService.postWithAuth,
  //     onSuccess: (data) {
  //       // _authUser = authUserFromJson(json.encode(data["data"]));
  //       var notificationModel = NotificationResponse.fromJson(data);
  //       return apiResponse;
  //     },
  //   );
  // }
}

final notificationVm = ChangeNotifierProvider<NotificationVm>((ref) {
  return NotificationVm();
});
