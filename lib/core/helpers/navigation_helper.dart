import 'package:equalcash/core/core.dart';
import 'package:page_transition/page_transition.dart';

/// A helper class for navigation operations throughout the app.
///
/// This class provides static methods to navigate to named routes or directly to widgets,
/// with support for transitions and arguments.
class NavigationHelper {
  /// Navigates to a named route using the app's global navigator.
  ///
  /// [routeName] The name of the route to navigate to.
  /// [args] Optional arguments to pass to the route.
  /// [replace] If true, replaces the current route instead of pushing a new one.
  /// [clearStack] If true, removes all existing routes before navigating.
  /// [duration] The duration of the transition animation.
  static Future<T?> navigateTo<T>({
    required String routeName,
    Object? args,
    bool replace = false,
    bool clearStack = false,
    Duration duration = const Duration(milliseconds: 300),
  }) async {
    final context = NavKey.appNavigatorKey.currentContext;

    if (context == null) {
      printty('Navigation context is null', logName: 'NavigationHelper');
      return null;
    }

    if (clearStack) {
      return Navigator.of(context).pushNamedAndRemoveUntil(
        routeName,
        (route) => false,
        arguments: args,
      );
    } else if (replace) {
      return Navigator.of(context).pushReplacementNamed(
        routeName,
        arguments: args,
      );
    } else {
      return Navigator.of(context).pushNamed(
        routeName,
        arguments: args,
      );
    }
  }

  /// Navigates directly to a widget using the app's global navigator.
  ///
  /// [screen] The widget to navigate to.
  /// [replace] If true, replaces the current route instead of pushing a new one.
  /// [clearStack] If true, removes all existing routes before navigating.
  /// [transition] The type of transition animation to use.
  /// [duration] The duration of the transition animation.
  static Future<T?> navigateToWidget<T>({
    required Widget screen,
    bool replace = false,
    bool clearStack = false,
    PageTransitionType transition = PageTransitionType.rightToLeft,
    Duration duration = const Duration(milliseconds: 300),
  }) async {
    final context = NavKey.appNavigatorKey.currentContext;

    if (context == null) {
      printty('Navigation context is null', logName: 'NavigationHelper');
      return null;
    }

    final pageRoute = PageTransition<T>(
      child: screen,
      type: transition,
      duration: duration,
    );

    if (clearStack) {
      return Navigator.of(context).pushAndRemoveUntil<T>(
        pageRoute,
        (route) => false,
      );
    } else if (replace) {
      return Navigator.of(context).pushReplacement<dynamic, T>(pageRoute)
          as Future<T?>;
    } else {
      return Navigator.of(context).push<T>(pageRoute);
    }
  }

  /// Navigates using the global navigator key, regardless of the current context.
  ///
  /// This is useful for navigating from services or other non-widget classes.
  ///
  /// [routeName] The name of the route to navigate to.
  /// [args] Optional arguments to pass to the route.
  /// [replace] If true, replaces the current route instead of pushing a new one.
  /// [clearStack] If true, removes all existing routes before navigating.
  static Future<T?> navigateToGlobal<T>({
    required String routeName,
    Object? args,
    bool replace = false,
    bool clearStack = false,
  }) async {
    final navigatorKey = NavKey.appNavigatorKey;

    if (navigatorKey.currentState == null) {
      printty('Navigator state is null', logName: 'NavigationHelper');
      return null;
    }

    if (clearStack) {
      return navigatorKey.currentState!.pushNamedAndRemoveUntil(
        routeName,
        (route) => false,
        arguments: args,
      );
    } else if (replace) {
      return navigatorKey.currentState!.pushReplacementNamed(
        routeName,
        arguments: args,
      );
    } else {
      return navigatorKey.currentState!.pushNamed(
        routeName,
        arguments: args,
      );
    }
  }

  /// Pops the current route from the navigation stack.
  ///
  /// [result] Optional result to return to the previous route.
  static void goBack<T>([T? result]) {
    final context = NavKey.appNavigatorKey.currentContext;

    if (context == null) {
      printty('Navigation context is null', logName: 'NavigationHelper');
      return;
    }

    Navigator.of(context).pop(result);
  }

  /// Pops multiple routes from the navigation stack.
  ///
  /// [count] The number of routes to pop.
  static void goBackMultiple(int count) {
    final context = NavKey.appNavigatorKey.currentContext;

    if (context == null) {
      printty('Navigation context is null', logName: 'NavigationHelper');
      return;
    }

    int popped = 0;
    Navigator.of(context).popUntil((route) {
      if (popped < count) {
        popped++;
        return false;
      }
      return true;
    });
  }
}
