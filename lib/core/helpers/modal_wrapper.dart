import 'package:equalcash/core/core.dart';

class ModalWrapper<T> {
  const ModalWrapper._();
  static Future<T?> bottomSheet<T>({
    required BuildContext context,
    required Widget widget,
    isScrollControlled = true,
    bool? canDismiss,
    double? topRadius,
    Color? color,
  }) {
    return showModalBottomSheet<T>(
      backgroundColor: color ?? Colors.transparent,
      isScrollControlled: true,
      isDismissible: canDismiss ?? true,
      enableDrag: false,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(topRadius ?? 16),
          topRight: Radius.circular(topRadius ?? 16),
        ),
      ),
      context: context,
      builder: (BuildContext bc) {
        return SizedBox(
          width: Sizer.screenWidth,
          child: widget,
        );
      },
    );
  }

  static void showCustomDialog(
    BuildContext context, {
    required Widget child,
    bool? canDismiss,
  }) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: canDismiss ?? true,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 700),
      pageBuilder: (_, __, ___) {
        return Center(
          child: Container(child: child),
        );
      },
      transitionBuilder: (_, anim, __, child) {
        Tween<Offset> tween;
        if (anim.status == AnimationStatus.reverse) {
          tween = Tween(begin: const Offset(0, 1), end: Offset.zero);
        } else {
          tween = Tween(begin: const Offset(0, 1), end: Offset.zero);
        }

        return SlideTransition(
          position: tween.animate(anim),
          child: FadeTransition(
            opacity: anim,
            child: child,
          ),
        );
      },
    );
  }

  static void showAlertDialog(
    BuildContext context, {
    String? title,
    String? content,
    String? rightBtnText,
    String? leftBtnText,
    VoidCallback? rightBtnOnTap,
    VoidCallback? leftBtnOnTap,
    TextStyle? contentTextStyle,
  }) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: title == null
            ? null
            : Text(
                title,
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.neutral400,
                ),
              ),
        content: Padding(
          padding: EdgeInsets.symmetric(vertical: Sizer.height(8)),
          child: Text(
            content ?? '',
            style: contentTextStyle ??
                AppTypography.text12.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral300,
                ),
          ),
        ),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            onPressed: leftBtnOnTap ??
                () {
                  Navigator.pop(context);
                },
            child: Text(
              leftBtnText ?? 'No',
              style: AppTypography.text14.copyWith(
                color: AppColors.purple89,
              ),
            ),
          ),
          CupertinoDialogAction(
            onPressed: rightBtnOnTap ??
                () {
                  Navigator.pop(context);
                },
            child: Text(
              rightBtnText ?? 'Yes',
              style: AppTypography.text14.copyWith(
                color: AppColors.purple89,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ModalLine extends StatelessWidget {
  const ModalLine({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 3,
      width: 40,
      color: AppColors.gray62,
    );
  }
}
