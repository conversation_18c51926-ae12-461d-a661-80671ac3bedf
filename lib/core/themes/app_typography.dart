import 'package:equalcash/core/core.dart';

class AppTypography {
  static TextStyle text8 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.black,
    fontSize: Sizer.text(8),
  );

  static TextStyle text10 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.black,
    fontSize: Sizer.text(10),
    height: 1.2,
  );

  static TextStyle text12 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.black,
    fontSize: Sizer.text(12),
    height: 1.2,
  );

  static TextStyle text13 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.black,
    fontSize: Sizer.text(13),
    height: 1.2,
  );

  static TextStyle text14 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.black,
    fontSize: Sizer.text(14),
    height: 1.2,
  );

  static TextStyle text15 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.black,
    fontSize: Sizer.text(15),
    height: 1.2,
  );

  static TextStyle text16 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.black,
    fontSize: Sizer.text(16),
    height: 1.2,
  );

  static TextStyle text18 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.black,
    fontSize: Sizer.text(18),
    height: 1.2,
  );

  static TextStyle text20 = TextStyle(
    color: AppColors.black,
    fontSize: Sizer.text(20),
    height: 1.2,
  );

  static TextStyle text24 = TextStyle(
    color: AppColors.black,
    fontSize: Sizer.text(24),
    height: 1.2,
  );

  static TextStyle text26 = TextStyle(
    color: AppColors.black,
    fontSize: Sizer.text(26),
    height: 1.2,
  );

  static TextStyle text28 = TextStyle(
    color: AppColors.black,
    fontSize: Sizer.text(28),
    height: 1.2,
  );

  static TextStyle text30 = TextStyle(
    color: AppColors.black,
    fontSize: Sizer.text(30),
    height: 1.2,
  );

  static TextStyle text32 = TextStyle(
    color: AppColors.black,
    fontSize: Sizer.text(32),
    height: 1.2,
  );

  static TextStyle text36 = TextStyle(
    color: AppColors.black,
    fontSize: Sizer.text(36),
    height: 1.2,
  );

  static TextStyle text48 = TextStyle(
    color: AppColors.black,
    fontSize: Sizer.text(48),
    height: 1.2,
  );
}
