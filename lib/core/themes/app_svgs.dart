// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

const String svgIcon = "assets/svgs";

class AppSvgs {
  static const String success = "$svgIcon/success.svg";
  static const String error = "$svgIcon/error.svg";

  static const String vLine = "$svgIcon/vLine.svg";
  static const String arrowBack = "$svgIcon/arrowBack.svg";
  static const String emojiPerson = "$svgIcon/emojiPerson.svg";
  static const String skipArrowRight = "$svgIcon/skipArrowRight.svg";
  static const String faceId = "$svgIcon/faceId.svg";
  static const String lock = "$svgIcon/lock.svg";
  static const String key = "$svgIcon/key.svg";
  static const String shield = "$svgIcon/shield.svg";
  static const String shieldSecurity = "$svgIcon/shieldSecurity.svg";

  static const String userActive = "$svgIcon/userActive.svg";
  static const String search = "$svgIcon/search.svg";
  static const String menu = "$svgIcon/menu.svg";
  static const String link = "$svgIcon/link.svg";

  static const String arrowUp = "$svgIcon/arrowUp.svg";
  static const String arrowDown = "$svgIcon/arrowDown.svg";

  static const String scanDash = "$svgIcon/scanDash.svg";
  static const String tickSquare = "$svgIcon/tickSquare.svg";
  static const String sim = "$svgIcon/sim.svg";
  static const String visa = "$svgIcon/visa.svg";

  // Socials
  static const String fb = "$svgIcon/socials/fb.svg";
  static const String ln = "$svgIcon/socials/ln.svg";
  static const String ig = "$svgIcon/socials/ig.svg";
  static const String x = "$svgIcon/socials/x.svg";
}

SizedBox svgHelper(
  String svg, {
  double? height,
  double? width,
  Color? color,
  ColorFilter? colorFilter,
  BoxFit? fit,
}) {
  return SizedBox(
    height: height,
    width: width,
    child: SvgPicture.asset(
      svg,
      fit: fit ?? BoxFit.cover,
      color: color,
      colorFilter: colorFilter,
    ),
  );
}
