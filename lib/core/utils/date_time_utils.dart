/// Utility functions for DateTime operations
class DateTimeUtils {
  /// Formats a DateTime into a "time ago" string (e.g., "2 hours ago", "3 days ago")
  /// 
  /// If the time difference is:
  /// - Less than a minute: "Just now"
  /// - Less than an hour: "X minutes ago"
  /// - Less than a day: "X hours ago"
  /// - Less than a week: "X days ago"
  /// - Less than a month: "X weeks ago"
  /// - Less than a year: "X months ago"
  /// - More than a year: "X years ago"
  static String timeAgo(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    // Less than a minute
    if (difference.inSeconds < 60) {
      return 'Just now';
    }
    
    // Less than an hour
    if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
    }
    
    // Less than a day
    if (difference.inHours < 24) {
      final hours = difference.inHours;
      return '$hours ${hours == 1 ? 'hour' : 'hours'} ago';
    }
    
    // Less than a week
    if (difference.inDays < 7) {
      final days = difference.inDays;
      return '$days ${days == 1 ? 'day' : 'days'} ago';
    }
    
    // Less than a month (approximately)
    if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
    }
    
    // Less than a year
    if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    }
    
    // More than a year
    final years = (difference.inDays / 365).floor();
    return '$years ${years == 1 ? 'year' : 'years'} ago';
  }
  
  /// Formats a DateTime into a short date string (e.g., "Jan 1, 2023")
  static String shortDate(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    final month = months[dateTime.month - 1];
    final day = dateTime.day;
    final year = dateTime.year;
    
    return '$month $day, $year';
  }
  
  /// Formats a DateTime into a time string (e.g., "3:45 PM")
  static String timeString(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    final hour = dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final hourIn12 = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '$hourIn12:$minute $period';
  }
}
