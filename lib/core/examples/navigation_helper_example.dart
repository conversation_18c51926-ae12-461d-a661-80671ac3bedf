import 'package:equalcash/core/core.dart';

/// This file contains examples of how to use the NavigationHelper class.
/// It is not meant to be used in production code.

class NavigationHelperExample {
  // Example 1: Navigate to a named route
  static void navigateToLoginScreen() {
    NavigationHelper.navigateTo(
      routeName: RoutePath.loginScreen,
    );
  }

  // Example 2: Navigate to a named route with arguments
  static void navigateToRepeatPasscodeScreen(String passcode) {
    NavigationHelper.navigateTo(
      routeName: RoutePath.repeatPasscodeScreen,
      args: passcode,
    );
  }

  // Example 3: Navigate to a named route and replace the current route
  static void navigateToOnboardingAndReplace() {
    NavigationHelper.navigateTo(
      routeName: RoutePath.onboardingScreen,
      replace: true,
    );
  }

  // Example 4: Navigate to a named route and clear the navigation stack
  static void navigateToDashboardAndClearStack() {
    NavigationHelper.navigateTo(
      routeName: RoutePath.dashboardNavigationScreen,
      clearStack: true,
    );
  }

  // Example 5: Navigate directly to a widget
  static void navigateToCustomWidget() {
    NavigationHelper.navigateToWidget(
      screen: const Scaffold(
        body: Center(
          child: Text('Custom Widget Screen'),
        ),
      ),
    );
  }

  // Example 6: Navigate to a named route using the global navigator
  static void navigateToLoginScreenGlobally() {
    NavigationHelper.navigateToGlobal(
      routeName: RoutePath.loginScreen,
    );
  }

  // Example 7: Go back to the previous screen
  static void goBackToPreviousScreen() {
    NavigationHelper.goBack();
  }

  // Example 8: Go back with a result
  static void goBackWithResult(String result) {
    NavigationHelper.goBack(result);
  }

  // Example 9: Go back multiple screens
  static void goBackTwoScreens() {
    NavigationHelper.goBackMultiple(2);
  }
}
