import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class CreateOrJoinCompanyWidget extends StatelessWidget {
  const CreateOrJoinCompanyWidget({
    super.key,
    this.contributionType = ContributionType.create,
    this.onTap,
  });

  final ContributionType contributionType;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.height(400),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Iconsax.chart_square5,
              color: AppColors.pri100,
              size: Sizer.radius(100),
            ),
            const YBox(24),
            Text(
              'Yet to ${(contributionType.value).toLowerCase()} a company',
              style: AppTypography.text20.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.neutral400,
              ),
            ),
            const YBox(8),
            Text(
              'Create a company to start saving. \nYour saving journey begins with it.',
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w400,
                color: AppColors.gray87,
                height: 1.3,
              ),
            ),
            const YBox(30),
            CustomBtn.solid(
              text: '${contributionType.value} a Company',
              onTap: onTap,
            ),
          ],
        ),
      ),
    );
  }
}
