import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class JoinedCompanyTab extends ConsumerStatefulWidget {
  const JoinedCompanyTab({super.key});

  @override
  ConsumerState<JoinedCompanyTab> createState() => _JoinedCompanyTabState();
}

class _JoinedCompanyTabState extends ConsumerState<JoinedCompanyTab> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(companyVmodel).getJoinedCompanies();
    });
  }

  @override
  Widget build(BuildContext context) {
    final companyRef = ref.watch(companyVmodel);
    return Expanded(
      child: LoadableContentBuilder(
        isBusy: companyRef.busy(joinedCompState),
        items: companyRef.companiesJoined,
        loadingBuilder: (context) {
          return ListView.separated(
            padding: EdgeInsets.only(
              top: Sizer.height(16),
              bottom: Sizer.height(100),
            ),
            itemBuilder: (ctx, i) {
              return const Skeletonizer(
                enabled: true,
                child: HomeCashBucketCard(),
              );
            },
            separatorBuilder: (_, __) => const YBox(16),
            itemCount: 8,
          );
        },
        emptyBuilder: (context) {
          return CreateOrJoinCompanyWidget(
            onTap: () {
              NavigationHelper.navigateTo(
                routeName: RoutePath.startCompanyScreen,
              );
            },
          );
        },
        contentBuilder: (context) {
          return ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(
              top: Sizer.height(16),
              bottom: Sizer.height(100),
            ),
            itemBuilder: (ctx, i) {
              final c = companyRef.companiesJoined[i];
              return HomeCashBucketCard(company: c);
            },
            separatorBuilder: (_, __) => const YBox(16),
            itemCount: companyRef.companiesJoined.length,
          );
        },
      ),
    );
  }
}
