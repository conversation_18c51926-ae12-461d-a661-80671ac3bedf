import 'package:equalcash/core/core.dart';

class ColumnText extends StatelessWidget {
  const ColumnText({
    super.key,
    required this.title,
    required this.subtitle,
  });

  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          textAlign: TextAlign.center,
          style: AppTypography.text14.copyWith(
            color: AppColors.neutral200,
          ),
        ),
        const YBox(8),
        Text(
          subtitle,
          textAlign: TextAlign.center,
          style: AppTypography.text16.copyWith(
            color: AppColors.neutral400,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
