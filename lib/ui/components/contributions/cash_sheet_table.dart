import 'package:equalcash/core/core.dart';

class CashSheetTable extends StatelessWidget {
  const CashSheetTable({
    super.key,
    required this.company,
  });

  final CompanyModel company;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            'Cash Sheet',
            style: AppTypography.text18.copyWith(
              color: AppColors.neutral400,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const YBox(8),
        Card(
          color: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Table(
            border: const TableBorder(
              horizontalInside: BorderSide(
                color: AppColors.secondary100,
                width: 1,
              ),
              verticalInside: BorderSide(
                color: AppColors.secondary100,
                width: 1,
              ),
            ),
            columnWidths: const {
              0: FlexColumnWidth(0.5),
              1: FlexColumnWidth(0.5),
            },
            defaultColumnWidth: const FlexColumnWidth(0.5),
            children: [
              const TableRow(
                children: [
                  CustomTableCell(text: ''),
                  CustomTableCell(text: 'Naira'),
                ],
              ),
              TableRow(
                children: [
                  const CustomTableCell(text: 'Stakeholders'),
                  CustomTableCell(
                      text: '${company.numberOfStakeholders} for now',
                      isValue: true),
                ],
              ),
              TableRow(
                children: [
                  const CustomTableCell(text: 'Cash Bucket'),
                  CustomTableCell(
                      text: AppUtils.formatNumber(
                          number:
                              double.tryParse(company.cashBucket ?? '0') ?? 0),
                      isValue: true),
                ],
              ),
              TableRow(
                children: [
                  const CustomTableCell(text: 'Cash Drop'),
                  CustomTableCell(
                      text: AppUtils.formatNumber(
                          number:
                              double.tryParse(company.cashDrop ?? '0') ?? 0),
                      isValue: true),
                ],
              ),
              TableRow(
                children: [
                  const CustomTableCell(text: 'Cash Reserve Ratio'),
                  CustomTableCell(
                      text: '${company.cashReserveRatio}', isValue: true),
                ],
              ),
              TableRow(
                children: [
                  const CustomTableCell(text: 'Monthly Cash Collection'),
                  CustomTableCell(
                      text: AppUtils.formatNumber(
                          number:
                              double.tryParse(company.cashDrop ?? '0') ?? 0),
                      isValue: true),
                ],
              ),
              const TableRow(
                children: [
                  CustomTableCell(text: 'Final Cash Collection'),
                  CustomTableCell(text: '10,000', isValue: true),
                ],
              ),
              const TableRow(
                children: [
                  CustomTableCell(text: 'Collection Order Fee'),
                  CustomTableCell(text: '', isValue: true),
                ],
              ),
              TableRow(
                children: [
                  const CustomTableCell(text: 'Commencement Date'),
                  CustomTableCell(
                      text: AppUtils.daymy(
                          company.commencementDate ?? DateTime.now()),
                      isValue: true),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class CustomTableCell extends StatelessWidget {
  const CustomTableCell({
    super.key,
    required this.text,
    this.isValue = false,
  });

  final String text;
  final bool isValue;

  @override
  Widget build(BuildContext context) {
    return TableCell(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
          vertical: Sizer.height(16),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontWeight: isValue ? FontWeight.w400 : FontWeight.w500,
            fontSize: Sizer.text(isValue ? 14 : 12),
            color: isValue ? AppColors.neutral300 : AppColors.neutral400,
          ),
        ),
      ),
    );
  }
}
