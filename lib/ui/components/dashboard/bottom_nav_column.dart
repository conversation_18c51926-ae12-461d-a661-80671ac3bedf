// ignore_for_file: deprecated_member_use

import 'package:equalcash/core/core.dart';

class BottomNavColumn extends StatelessWidget {
  const BottomNavColumn({
    super.key,
    required this.icon,
    required this.labelText,
    this.showIcon = true,
    this.isSelected = false,
    required this.onPressed,
  });

  final dynamic icon;
  final String labelText;
  final bool showIcon;
  final bool isSelected;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: onPressed,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon)
            (icon is String)
                ? SvgPicture.asset(
                    icon,
                    height: Sizer.height(24),
                    width: Sizer.width(24),
                  )
                : Icon(
                    icon,
                    color: isSelected
                        ? AppColors.white
                        : AppColors.white.withOpacity(0.7),
                    size: Sizer.height(24),
                  ),
          const YBox(6),
          Container(
            padding: EdgeInsets.only(
              top: !showIcon ? Sizer.height(20) : 0,
            ),
            child: Text(
              labelText,
              style: AppTypography.text12.copyWith(
                color: isSelected
                    ? AppColors.white
                    : AppColors.white.withOpacity(0.7),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
