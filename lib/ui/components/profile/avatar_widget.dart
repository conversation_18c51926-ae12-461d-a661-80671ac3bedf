import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class AvatarWidget extends ConsumerWidget {
  const AvatarWidget({
    super.key,
    this.borderColor,
  });

  final Color? borderColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileRef = ref.watch(profileVm);
    return InkWell(
      onTap: () {
        ModalWrapper.showCustomDialog(
          context,
          child: const TakePhotoModal(),
        );
      },
      child: Container(
        height: Sizer.height(96),
        width: Sizer.width(96),
        padding: EdgeInsets.all(Sizer.radius(4)),
        decoration: BoxDecoration(
          color: borderColor ?? AppColors.white,
          shape: BoxShape.circle,
        ),
        child: profileRef.profilePicture != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(Sizer.radius(100)),
                child: MyCachedNetworkImage(
                  imageUrl: profileRef.profilePicture!,
                  fit: BoxFit.cover,
                ),
              )
            : Container(
                decoration: const BoxDecoration(
                  color: AppColors.primaryPurple,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Iconsax.camera,
                  size: Sizer.radius(40),
                  color: AppColors.accent50,
                ),
              ),
      ),
    );
  }
}
