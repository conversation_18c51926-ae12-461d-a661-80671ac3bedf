import 'package:equalcash/core/core.dart';

class ProfileListTile extends StatelessWidget {
  const ProfileListTile({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.titleStyle,
    this.trailingWidget,
    this.onTap,
  });

  final String title;
  final String? subtitle;
  final dynamic icon;
  final TextStyle? titleStyle;
  final Widget? trailingWidget;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(12)),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(Sizer.radius(100)),
            ),
            child: icon is String
                ? SvgPicture.asset(
                    icon,
                    height: Sizer.height(24),
                  )
                : Icon(
                    icon,
                    size: Sizer.radius(24),
                    color: AppColors.primaryPurple,
                  ),
          ),
          const XBox(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: titleStyle ??
                      AppTypography.text16.copyWith(
                        fontWeight: FontWeight.w400,
                        color: AppColors.neutral400,
                      ),
                ),
                if (subtitle != null) const YBox(4),
                if (subtitle != null)
                  Text(
                    subtitle ?? '',
                    style: AppTypography.text12.copyWith(
                      color: AppColors.neutral200,
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(
            child: trailingWidget ??
                const Icon(
                  Iconsax.arrow_right_3,
                ),
          )
        ],
      ),
    );
  }
}
