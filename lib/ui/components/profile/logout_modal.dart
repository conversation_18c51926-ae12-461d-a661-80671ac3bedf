import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class LogoutModal extends StatelessWidget {
  const LogoutModal({
    super.key,
    // required this.title,
    // required this.content,
    // this.btnText,
    this.onAction,
  });

  // final String title;
  // final String content;
  // final String? btnText;
  final Function()? onAction;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(Sizer.radius(24)),
          topRight: Radius.circular(Sizer.radius(24)),
        ),
        color: AppColors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Align(
            alignment: Alignment.centerRight,
            child: InkWell(
              onTap: () => Navigator.pop(context),
              child: Text('Close',
                  style: AppTypography.text16.copyWith(
                    color: AppColors.neutral200,
                  )),
            ),
          ),
          const YBox(20),
          Icon(
            Iconsax.logout,
            size: Sizer.radius(56),
            color: AppColors.primaryPurple,
          ),
          const YBox(16),
          Text(
            'Adebayo, we’re sorry to see you go',
            style: AppTypography.text16.copyWith(
              color: AppColors.neutral300,
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(10),
          Text(
            'Are you sure you want to log out of your \naccount? You won’t be able to get in-app \nupdates and transaction alerts.',
            textAlign: TextAlign.center,
            style: AppTypography.text14.copyWith(
              color: AppColors.neutral200,
              height: 1.4,
            ),
          ),
          const YBox(24),
          CustomBtn.solid(
            text: 'Cancel',
            onTap: () => Navigator.pop(context),
          ),
          const YBox(16),
          TextButton(
              onPressed: onAction,
              child: Text(
                'Yes, Log me out',
                style: AppTypography.text16.copyWith(
                  color: AppColors.neutral200,
                ),
              )),
          const YBox(40),
        ],
      ),
    );
  }
}
