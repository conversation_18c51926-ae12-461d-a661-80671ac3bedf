import 'package:equalcash/core/core.dart';

class TransactionListTile extends StatelessWidget {
  const TransactionListTile({
    super.key,
    this.onTap,
  });

  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.width(10)),
            decoration: BoxDecoration(
              color: AppColors.gray87.withOpacity(0.1),
              borderRadius: BorderRadius.circular(
                Sizer.radius(30),
              ),
            ),
            child: svg<PERSON>elper(AppSvgs.arrowDown),
          ),
          const XBox(12),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(children: [
                    TextSpan(
                      text: 'Withdrawal ',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral400,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Nohemi',
                      ),
                    ),
                    TextSpan(
                      text: '(From debit card)',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral200,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Nohemi',
                      ),
                    ),
                  ]),
                ),
                const YBox(6),
                Row(
                  children: [
                    Text(
                      'Jan 28, 2024',
                      style: AppTypography.text12.copyWith(
                        color: AppColors.neutral200,
                      ),
                    ),
                    const XBox(16),
                    Text(
                      '02:40pm',
                      style: AppTypography.text12.copyWith(
                        color: AppColors.neutral200,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          RichText(
            text: TextSpan(children: [
              TextSpan(
                text: '₦',
                style: AppTypography.text14.copyWith(
                  color: AppColors.gray32,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: '20,000 ',
                style: AppTypography.text14.copyWith(
                  color: AppColors.gray32,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Nohemi',
                ),
              ),
            ]),
          ),
        ],
      ),
    );
  }
}
