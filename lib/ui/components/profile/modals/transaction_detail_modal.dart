import 'package:equalcash/core/core.dart';

class TransactionDetailModal extends StatefulWidget {
  const TransactionDetailModal({super.key});

  @override
  State<TransactionDetailModal> createState() => _TransactionDetailModalState();
}

class _TransactionDetailModalState extends State<TransactionDetailModal> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: Sizer.width(0),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Container(
          width: MediaQuery.of(context).size.width,
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
            vertical: Sizer.height(20),
          ),
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: Icon(
                  Icons.close,
                  color: AppColors.neutral300,
                  size: Sizer.height(28),
                ),
              ),
              const YBox(28),
              const TransactionActionText(
                  titleText: 'Transaction Type:', trailingText: 'Withdrawal'),
              const YBox(16),
              const TransactionActionText(
                  titleText: 'From:', trailingText: 'Debit Card'),
              const YBox(16),
              const TransactionActionText(
                  titleText: 'Amount:', trailingText: 'N20,000'),
              const YBox(16),
              const TransactionActionText(
                  titleText: 'Date:', trailingText: 'Jan 28, 2024'),
              const YBox(16),
              const TransactionActionText(
                  titleText: 'Time:', trailingText: '02:40pm'),
              const YBox(16),
            ],
          ),
        ),
      ),
    );
  }
}

class TransactionActionText extends StatelessWidget {
  const TransactionActionText({
    super.key,
    required this.titleText,
    required this.trailingText,
  });

  final String titleText;
  final String trailingText;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: Sizer.height(5),
        horizontal: Sizer.width(10),
      ),
      child: Row(
        children: [
          Text(
            titleText,
            style: AppTypography.text16.copyWith(
              color: AppColors.gray87,
            ),
          ),
          const Spacer(),
          Text(
            trailingText,
            style: AppTypography.text16.copyWith(
              color: AppColors.gray32,
            ),
          ),
        ],
      ),
    );
  }
}
