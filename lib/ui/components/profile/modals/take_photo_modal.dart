import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class TakePhotoModal extends ConsumerStatefulWidget {
  const TakePhotoModal({super.key});

  @override
  ConsumerState<TakePhotoModal> createState() => _TakePhotoModalState();
}

class _TakePhotoModalState extends ConsumerState<TakePhotoModal> {
  bool _isUploading = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: Sizer.width(30),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: Sizer.height(_isUploading ? 200 : 160),
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(30),
          ),
          color: Colors.white,
          child: _isUploading ? _buildUploadingState() : _buildSelectionState(),
        ),
      ),
    );
  }

  Widget _buildSelectionState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _actionText(
            'Take photo', () => _handleImageSelection(ImageSource.camera)),
        const YBox(16),
        _actionText('Choose from gallery',
            () => _handleImageSelection(ImageSource.gallery)),
      ],
    );
  }

  Widget _buildUploadingState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const LoaderIcon(size: 40),
        const YBox(16),
        Text(
          'Updating profile picture...',
          style: AppTypography.text16.copyWith(
            color: AppColors.neutral300,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _actionText(String text, Function() onTap) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(5),
        ),
        child: Text(
          text,
          style: AppTypography.text16.copyWith(
            color: AppColors.neutral300,
          ),
        ),
      ),
    );
  }

  Future<void> _handleImageSelection(ImageSource source) async {
    try {
      // Check and request permissions
      // final hasPermission = await _checkAndRequestPermission(source);
      // if (!hasPermission) {
      //   _showPermissionDeniedMessage(source);
      //   return;
      // }

      // Pick image
      final images = await ImagePickerUtils.pickImage(
        source: source,
        imageQuality: 85, // Reduce quality for faster upload
        multiImage: false,
      );

      if (images.isEmpty) {
        // User cancelled image selection
        return;
      }

      final selectedImage = images.first;

      // Crop image
      final croppedImage =
          await ImagePickerUtils.cropImage(image: selectedImage);
      if (croppedImage == null) {
        // User cancelled cropping
        return;
      }

      // Validate image size (optional - add size limit if needed)
      if (!_validateImageSize(croppedImage)) {
        _showErrorMessage(
            'Image size is too large. Please select a smaller image.');
        return;
      }

      // Upload image
      await _uploadProfilePicture(croppedImage);
    } catch (e) {
      _showErrorMessage('Failed to select image. Please try again.');
    }
  }

  // Future<bool> _checkAndRequestPermission(ImageSource source) async {
  //   Permission permission =
  //       source == ImageSource.camera ? Permission.camera : Permission.photos;

  //   PermissionStatus status = await permission.status;

  //   if (status.isGranted) {
  //     return true;
  //   }

  //   if (status.isDenied) {
  //     status = await permission.request();
  //     return status.isGranted;
  //   }

  //   if (status.isPermanentlyDenied) {
  //     // Show dialog to open app settings
  //     _showPermissionSettingsDialog(source);
  //     return false;
  //   }

  //   return false;
  // }

  bool _validateImageSize(File image) {
    // Check if image size is less than 10MB
    final fileSizeInBytes = image.lengthSync();
    final fileSizeInMB = fileSizeInBytes / (1024 * 1024);
    return fileSizeInMB <= 10;
  }

  Future<void> _uploadProfilePicture(File image) async {
    setState(() {
      _isUploading = true;
    });

    try {
      final profileVmInstance = ref.read(profileVm);
      final response =
          await profileVmInstance.updateProfilePicture(image: image);

      if (mounted) {
        setState(() {
          _isUploading = false;
        });

        handleApiResponse(
          response: response,
          onCompleted: () {
            Navigator.of(context).pop();
            _showSuccessMessage('Profile picture updated successfully!');
          },
          onError: () {
            _showErrorMessage(
                response.message ?? 'Failed to update profile picture');
          },
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
        _showErrorMessage(
            'Network error. Please check your connection and try again.');
      }
    }
  }

  void _showSuccessMessage(String message) {
    FlushBarToast.fLSnackBar(
      message: message,
      snackBarType: SnackBarType.success,
      duration: 3,
    );
  }

  void _showErrorMessage(String message) {
    FlushBarToast.fLSnackBar(
      message: message,
      snackBarType: SnackBarType.warning,
      duration: 4,
    );
  }

  void _showPermissionDeniedMessage(ImageSource source) {
    final sourceText =
        source == ImageSource.camera ? 'camera' : 'photo library';
    _showErrorMessage(
        'Permission denied. Please allow access to $sourceText in settings.');
  }

  void _showPermissionSettingsDialog(ImageSource source) {
    final sourceText =
        source == ImageSource.camera ? 'camera' : 'photo library';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Permission Required'),
        content: Text(
            'Please allow access to $sourceText in app settings to continue.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }
}
