import 'package:equalcash/core/core.dart';

/// A reusable widget for displaying card information fields
/// Used for showing label-value pairs on credit/debit cards
class CardInfoField extends StatelessWidget {
  /// The label text (e.g., "Card Holder", "Expiry Date")
  final String label;
  
  /// The value text (e.g., "John Doe", "12/25")
  final String value;
  
  /// Optional color for the label text
  final Color? labelColor;
  
  /// Optional color for the value text
  final Color? valueColor;
  
  /// Optional text style for the label
  final TextStyle? labelStyle;
  
  /// Optional text style for the value
  final TextStyle? valueStyle;
  
  /// Space between label and value
  final double spacing;

  const CardInfoField({
    super.key,
    required this.label,
    required this.value,
    this.labelColor,
    this.valueColor,
    this.labelStyle,
    this.valueStyle,
    this.spacing = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Label text
        Text(
          label,
          style: labelStyle ??
              AppTypography.text10.copyWith(
                color: labelColor ?? AppColors.white.withValues(alpha: 0.5),
              ),
        ),
        
        // Spacing between label and value
        SizedBox(height: Sizer.height(spacing)),
        
        // Value text
        Text(
          value,
          style: valueStyle ??
              AppTypography.text12.copyWith(
                color: valueColor ?? AppColors.white.withValues(alpha: 0.8),
              ),
        ),
      ],
    );
  }
}
