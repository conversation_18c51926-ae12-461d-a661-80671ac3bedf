import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// A reusable widget for displaying a credit/debit card
class CreditCardView extends StatelessWidget {
  /// Card number (masked or unmasked)
  final String cardNumber;

  /// Card holder name
  final String cardHolder;

  /// Expiry date in MM/YY format
  final String expiryDate;

  /// CVV number
  final String cvv;

  /// Card type logo (e.g., Visa, Mastercard)
  final String? cardTypeLogo;

  /// Background image for the card
  final String? backgroundImage;

  /// Background color for the card (used if no image is provided)
  final Color? backgroundColor;

  /// Whether to show the card details or just a placeholder
  final bool showDetails;

  /// Card width
  final double? width;

  /// Card height
  final double? height;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  const CreditCardView({
    super.key,
    required this.cardNumber,
    required this.cardHolder,
    required this.expiryDate,
    required this.cvv,
    this.cardTypeLogo,
    this.backgroundImage,
    this.backgroundColor,
    this.showDetails = true,
    this.width,
    this.height,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: height ?? Sizer.height(200),
        width: width ?? Sizer.screenWidth,
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.primaryPurple,
          borderRadius: BorderRadius.circular(Sizer.radius(16)),
          image: backgroundImage != null
              ? DecorationImage(
                  image: AssetImage(backgroundImage!),
                  fit: BoxFit.cover,
                )
              : null,
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SvgPicture.asset(AppSvgs.sim),
                  if (cardTypeLogo != null)
                    SvgPicture.asset(cardTypeLogo!)
                  else
                    SvgPicture.asset(AppSvgs.visa),
                ],
              ),
              const Spacer(),
              CardInfoField(
                label: 'Card Number',
                value: showDetails
                    ? cardNumber
                    : '****   ****   ****   ****   1234',
                valueStyle: AppTypography.text14.copyWith(
                  color: AppColors.white.withValues(alpha: 0.8),
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  Expanded(
                    flex: 5,
                    child: CardInfoField(
                      label: 'Card Holder',
                      value: showDetails ? cardHolder : 'XXXX XXXX',
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: CardInfoField(
                      label: 'Expiry Date',
                      value: showDetails ? expiryDate : 'XX/XX',
                    ),
                  ),
                  CardInfoField(
                    label: 'Cvv',
                    value: showDetails ? cvv : 'XXX',
                  ),
                ],
              ),
              const YBox(20),
            ],
          ),
        ),
      ),
    );
  }
}
