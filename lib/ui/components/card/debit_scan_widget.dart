import 'package:equalcash/core/core.dart';

class DebitScanWidget extends StatelessWidget {
  const DebitScanWidget({
    super.key,
    required this.icon,
    required this.text,
    this.color,
    this.onTap,
  });

  final String icon;
  final String text;
  final Color? color;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          svgHelper(icon),
          const XBox(8),
          Text(
            text,
            style: AppTypography.text14.copyWith(
              color: color ?? AppColors.primaryPurple,
            ),
          ),
        ],
      ),
    );
  }
}
