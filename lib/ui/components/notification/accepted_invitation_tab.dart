import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class AcceptedInvitationTab extends StatelessWidget {
  const AcceptedInvitationTab({
    super.key,
    required this.company,
  });

  final CompanyModel company;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Accepted Invitations (${company.numberOfStakeholders})',
          style: AppTypography.text18.copyWith(
            color: AppColors.neutral400,
          ),
        ),
        const YBox(12),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (ctx, i) {
            final member = company.members?[i];
            return InviteTile(
              invitee: member?.userName ?? member?.invitationIdentifier ?? "",
              status: member?.invitationStatus ?? '',
            );
          },
          separatorBuilder: (_, __) => const YBox(24),
          itemCount: company.members?.length ?? 0,
        )
      ],
    );
  }
}
