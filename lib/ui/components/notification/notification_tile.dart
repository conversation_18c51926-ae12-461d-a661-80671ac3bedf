import 'package:equalcash/core/core.dart';

class NotificationTile extends StatelessWidget {
  const NotificationTile({
    super.key,
    this.onTap,
    required this.title,
    required this.subtitle,
    required this.createdAt,
  });

  final Function()? onTap;
  final String? title;
  final String? subtitle;
  final DateTime? createdAt;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(
          Sizer.radius(16),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(
            Sizer.radius(12),
          ),
          border: Border.all(
            color: AppColors.secondary100,
            width: 0,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subtitle ?? '',
                    style: AppTypography.text14.copyWith(
                      color: AppColors.neutral300,
                      height: 1.4,
                    ),
                  ),
                  const YBox(8),
                  Text(
                    DateTimeUtils.timeAgo(createdAt),
                    style: AppTypography.text12.copyWith(
                      color: AppColors.neutral200,
                    ),
                  ),
                ],
              ),
            ),
            const XBox(16),
            const Icon(
              Iconsax.arrow_right_3,
            )
          ],
        ),
      ),
    );
  }
}
