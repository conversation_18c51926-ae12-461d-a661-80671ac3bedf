import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class ActivitiesNotification extends ConsumerWidget {
  const ActivitiesNotification({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationRef = ref.watch(notificationVm);
    return LoadableContentBuilder(
      isBusy: notificationRef.isBusy,
      items: notificationRef.activitiesNotifications,
      loadingBuilder: (context) {
        return const SizedBox.shrink();
      },
      emptyBuilder: (context) {
        return const EmptyListState(
          text: 'No notifications found',
        );
      },
      contentBuilder: (context) {
        return ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (ctx, i) {
            final notification = notificationRef.activitiesNotifications[i];
            return Padding(
              padding: EdgeInsets.only(bottom: 16.h),
              child: NotificationTile(
                title: notification?.subject,
                subtitle: notification?.message,
                createdAt: notification?.createdAt,
                onTap: () {
                  switch (notification?.type) {
                    case "first_company_invitation":
                      NavigationHelper.navigateTo(
                        routeName: RoutePath.invitationDetailsScreen,
                        args: notification?.typePath ?? '',
                      );
                      break;
                    case "0":
                      NavigationHelper.navigateTo(
                        routeName: RoutePath.invitationScreen,
                      );
                      break;
                    case "1":
                      NavigationHelper.navigateTo(
                        routeName: RoutePath.invitationDetailsScreen,
                      );
                      break;
                    case "order_of_collection":
                      NavigationHelper.navigateTo(
                        routeName: RoutePath.orderOfCollectionScreen,
                        args: notification?.typePath ?? '',
                      );
                      break;
                    case "3":
                      NavigationHelper.navigateTo(
                        routeName: RoutePath.debitCardScreen,
                      );
                      break;
                    default:
                  }
                },
              ),
            );
          },
          itemCount: notificationRef.allNotifications.length,
        );
      },
    );
  }
}
