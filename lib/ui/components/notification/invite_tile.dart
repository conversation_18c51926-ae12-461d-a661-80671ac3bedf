import 'package:equalcash/core/core.dart';

class InviteTile extends StatelessWidget {
  const InviteTile({
    super.key,
    required this.status,
    required this.invitee,
  });

  final String status;
  final String invitee;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            invitee,
            style: AppTypography.text16.copyWith(
              color: AppColors.neutral300,
            ),
          ),
        ),
        Text(
          status,
          style: AppTypography.text14.copyWith(
            color: status.toLowerCase() ==
                    InvitationType.accepted.value.toLowerCase()
                ? AppColors.green00
                : AppColors.red00,
          ),
        )
      ],
    );
  }
}
