import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class TransactionNotification extends ConsumerWidget {
  const TransactionNotification({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationRef = ref.watch(notificationVm);
    return LoadableContentBuilder(
        isBusy: notificationRef.isBusy,
        items: notificationRef.transactionNotifications,
        loadingBuilder: (context) {
          return const SizedBox.shrink();
          // return ListView.separated(
          //   padding: EdgeInsets.only(
          //     top: Sizer.height(16),
          //     bottom: Sizer.height(100),
          //   ),
          //   itemBuilder: (ctx, i) {
          //     return Skeletonizer(
          //       enabled: true,
          //       child: NotificationTile(
          //         title: "Contribution Company Invitation",
          //         subtitle:
          //             "You are invited by Adebayo <PERSON> to join a monthly contribution scheme...",
          //         createdAt: DateTime.now(),
          //       ),
          //     );
          //   },
          //   separatorBuilder: (_, __) => const YBox(16),
          //   itemCount: 8,
          // );
        },
        emptyBuilder: (context) {
          return const EmptyListState(
            text: 'No notifications found',
          );
        },
        contentBuilder: (context) {
          return ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final notification =
                  notificationRef.transactionNotifications[index];
              return Padding(
                padding: EdgeInsets.only(bottom: 16.h),
                child: NotificationTile(
                  title: notification?.subject,
                  subtitle: notification?.message,
                  createdAt: notification?.createdAt,
                  onTap: () {
                    switch (notification?.type) {
                      case "first_company_invitation":
                        NavigationHelper.navigateTo(
                          routeName: RoutePath.invitationDetailsScreen,
                          args: notification?.typePath ?? '',
                        );
                        break;
                      case "0":
                        NavigationHelper.navigateTo(
                          routeName: RoutePath.invitationScreen,
                        );
                        break;
                      case "1":
                        NavigationHelper.navigateTo(
                          routeName: RoutePath.invitationDetailsScreen,
                        );
                        break;
                      case "2":
                        NavigationHelper.navigateTo(
                          routeName: RoutePath.orderOfCollectionScreen,
                        );
                        break;
                      case "3":
                        NavigationHelper.navigateTo(
                          routeName: RoutePath.debitCardScreen,
                        );
                        break;
                      default:
                    }
                  },
                ),
              );
            },
            itemCount: notificationRef.allNotifications.length,
          );
        });
  }
}
