import 'package:equalcash/core/core.dart';

class SkipForNow extends StatelessWidget {
  const SkipForNow({
    super.key,
    this.onTap,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Skip For Now',
            style: AppTypography.text16.copyWith(
              color: AppColors.pri300,
            ),
          ),
          const XBox(8),
          svgHelper(AppSvgs.skipArrowRight),
        ],
      ),
    );
  }
}
