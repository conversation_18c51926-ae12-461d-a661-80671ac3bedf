import 'package:equalcash/lib.dart';

class CustomBtn {
  static Widget solid({
    required Function()? onTap,
    bool online = true,
    bool isOutline = false,
    required String text,
    bool isLoading = false,
    BorderRadiusGeometry? borderRadius,
    double? width,
    double? height,
    Color? offlineColor,
    Color? onlineColor,
    Color? outlineColor,
    Color? textColor,
    TextStyle? textStyle,
  }) {
    return IgnorePointer(
      ignoring: !online || isLoading,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: width ?? Sizer.screenWidth,
          height: Sizer.height(height ?? 60),
          decoration: (online && !isLoading)
              ? BoxDecoration(
                  borderRadius: borderRadius ?? BorderRadius.circular(12),
                  color: isOutline
                      ? AppColors.transparent
                      : onlineColor ?? AppColors.primaryPurple,
                  border: isOutline
                      ? Border.all(
                          color: outlineColor ?? AppColors.primaryPurple)
                      : null,
                )
              : BoxDecoration(
                  borderRadius: borderRadius ?? BorderRadius.circular(12),
                  color: offlineColor ?? AppColors.purpleC9,
                ),
          child: Center(
            child: isLoading
                ? const FittedBox(child: LoaderIcon(size: 30))
                : Text(
                    text,
                    style: textStyle ??
                        AppTypography.text18.copyWith(
                          color: textColor ?? AppColors.white,
                        ),
                  ),
          ),
        ),
      ),
    );
  }

  static Widget withChild({
    required Function()? onTap,
    required bool online,
    BorderRadiusGeometry? borderRadius,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    Color? offlineColor,
    Color? onlineColor,
    required Widget child,
  }) {
    return IgnorePointer(
      ignoring: !online,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: width ?? Sizer.screenWidth,
          height: Sizer.height(height ?? 60),
          padding: padding ??
              EdgeInsets.symmetric(
                vertical: Sizer.height(14),
                horizontal: Sizer.width(10),
              ),
          decoration: online
              ? BoxDecoration(
                  borderRadius: borderRadius ?? BorderRadius.circular(12),
                  color: onlineColor ?? AppColors.primaryPurple,
                )
              : BoxDecoration(
                  borderRadius: borderRadius ?? BorderRadius.circular(12),
                  color: offlineColor ?? AppColors.purpleC9,
                ),
          child: Center(child: child),
        ),
      ),
    );
  }
}

class BtnLoadState extends StatelessWidget {
  const BtnLoadState({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Sizer.height(56),
      width: Sizer.screenWidth,
      decoration: BoxDecoration(
        color: AppColors.secondary100,
        borderRadius: BorderRadius.circular(Sizer.radius(12)),
      ),
      child: const Center(
        child: LoaderIcon(
          size: 30,
        ),
      ),
    );
  }
}
