import 'package:equalcash/core/core.dart';

class MiniBtn extends StatelessWidget {
  const MiniBtn({
    super.key,
    required this.text,
    this.isSelected = false,
    this.count,
    this.fontSize,
    this.onTap,
  });

  final String text;
  final String? count;
  final bool isSelected;
  final double? fontSize;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            margin: EdgeInsets.only(
              right: Sizer.width(10),
            ),
            padding: EdgeInsets.symmetric(
              vertical: Sizer.height(10),
              horizontal: Sizer.width(16),
            ),
            decoration: BoxDecoration(
              color: isSelected ? AppColors.primaryPurple : AppColors.pri100,
              borderRadius: BorderRadius.circular(
                Sizer.width(8),
              ),
            ),
            child: Center(
              child: Text(
                text,
                style: AppTypography.text14.copyWith(
                  color: isSelected ? AppColors.white : AppColors.primaryPurple,
                  fontSize: fontSize,
                ),
              ),
            ),
          ),
          if (count != null)
            Positioned(
              top: -5,
              right: -8,
              child: Container(
                // height: Sizer.height(18),
                // width: Sizer.height(18),
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(4),
                  vertical: Sizer.height(2),
                ),
                decoration: BoxDecoration(
                  color: AppColors.red05,
                  borderRadius: BorderRadius.circular(Sizer.width(20)),
                  border: Border.all(
                    color: AppColors.purpleF1,
                    width: Sizer.width(1),
                  ),
                ),
                child: Center(
                  child: Text(
                    count ?? '0',
                    style: AppTypography.text12.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
