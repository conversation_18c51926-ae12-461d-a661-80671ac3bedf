import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:flutter/services.dart';

class CustomTextField extends StatefulWidget {
  final String? labelText, hintText, optionalText;
  final int? maxLines;
  final TextEditingController? controller;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final bool? isPassword, isConfirmPassword, showSuffixIcon, showfillColor;
  final Widget? suffixIcon, prefix, prefixIcon;
  final KeyboardType keyboardType;
  final double? width, height, labelSize;
  final double? borderRadius;
  final bool? isReadOnly;
  final FocusNode? focusNode;
  final bool showLabelHeader, hideBorder;
  final Color? labelColor;
  final Color? fillColor;
  final Color? borderColor;
  final Color? textfieldColor;
  final TextAlign textAlign;
  final TextStyle? hintStyle;
  final EdgeInsetsGeometry? contentPadding;
  final bool? enableInteractiveSelection;
  final bool? showCursor;
  final TextInputType? inputType;
  final String? Function(String?)?
      validator; // Deprecated - use validationRules instead
  final Function()? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final bool isRequired; // Deprecated - use validationRules instead

  // New custom validation system parameters
  final List<ValidationRule>? validationRules;
  final Duration? validationDebounceDelay;
  final void Function(ValidationResult)? onValidationChanged;
  final bool enableRealTimeValidation;
  final bool showValidationStateIndicator;

  const CustomTextField({
    super.key,
    this.maxLines,
    this.labelText,
    this.hintText,
    this.optionalText,
    this.labelColor,
    this.textfieldColor,
    this.fillColor,
    this.borderColor,
    this.labelSize,
    this.controller,
    this.isPassword = false,
    this.isConfirmPassword = false,
    this.showSuffixIcon = false,
    this.hideBorder = false,
    this.showfillColor,
    this.suffixIcon,
    this.prefix,
    this.prefixIcon,
    this.width,
    this.height,
    this.borderRadius,
    this.isReadOnly = false,
    this.keyboardType = KeyboardType.regular,
    this.showLabelHeader = false,
    this.focusNode,
    this.onChanged,
    this.onSubmitted,
    this.textAlign = TextAlign.start,
    this.hintStyle,
    this.contentPadding,
    this.enableInteractiveSelection,
    this.showCursor,
    this.onTap,
    this.inputType,
    this.validator,
    this.inputFormatters,
    this.isRequired = false, // Default to false for optional fields

    // New validation system parameters
    this.validationRules,
    this.validationDebounceDelay = const Duration(milliseconds: 300),
    this.onValidationChanged,
    this.enableRealTimeValidation = true,
    this.showValidationStateIndicator = false,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  bool showPassword = false;
  String? currentError;
  bool _hasUserStartedTyping = false;
  ValidationManager? _validationManager;
  ValidationState _validationState = ValidationState.initial;

  @override
  void initState() {
    super.initState();

    // Initialize validation manager if validation rules are provided
    if (widget.validationRules != null && widget.validationRules!.isNotEmpty) {
      _validationManager = ValidationManager(
        rules: widget.validationRules!,
        debounceDelay:
            widget.validationDebounceDelay ?? const Duration(milliseconds: 300),
        onValidationChanged: _onValidationChanged,
      );
    }

    // Add listener to controller for automatic error clearing on programmatic text changes
    if (widget.controller != null) {
      widget.controller!.addListener(_onControllerTextChanged);
    }

    List<KeyboardType> numsKeyboardType = [
      KeyboardType.decimal,
      KeyboardType.number,
    ];
    // KeyboardOverlay.showOverlay(context);
    if (widget.focusNode != null &&
        numsKeyboardType.contains(widget.keyboardType)) {
      KeyboardOverlay.addRemoveFocusNode(context, widget.focusNode!);
    }
  }

  @override
  void dispose() {
    // Remove listener from controller
    if (widget.controller != null) {
      widget.controller!.removeListener(_onControllerTextChanged);
    }
    _validationManager?.dispose();
    super.dispose();
  }

  /// Callback for validation state changes
  void _onValidationChanged(ValidationResult result) {
    if (mounted) {
      setState(() {
        _validationState = result.state;

        // Update error display based on validation result and user typing state
        final hasContent = widget.controller?.text.trim().isNotEmpty ?? false;
        final shouldShowError = result.error != null &&
            (!hasContent ||
                !_hasUserStartedTyping ||
                result.state == ValidationState.invalid);

        currentError = shouldShowError ? result.error : null;
      });

      // Notify parent widget of validation changes
      widget.onValidationChanged?.call(result);
    }
  }

  /// Handle text changes for real-time validation and error clearing
  void _handleTextChange(String value) {
    // Check if user has started typing (field has content)
    final hasContent = value.trim().isNotEmpty;

    // Update typing state
    if (hasContent && !_hasUserStartedTyping) {
      setState(() {
        _hasUserStartedTyping = true;
      });
    } else if (!hasContent && _hasUserStartedTyping) {
      setState(() {
        _hasUserStartedTyping = false;
      });
    }

    // Clear error immediately when user starts typing valid content
    if (hasContent && currentError != null) {
      setState(() {
        currentError = null;
      });
    }

    // Perform validation using new system
    if (_validationManager != null && widget.enableRealTimeValidation) {
      if (hasContent || !_hasUserStartedTyping) {
        _validationManager!.validateWithDebounce(value);
      }
    } else {
      // Fallback to legacy validation
      _performLegacyValidation(value);
    }

    // Call the original onChanged callback if provided
    widget.onChanged?.call(value);
  }

  /// Handle controller text changes (for programmatic text setting)
  /// This method is called when text is set programmatically via controller.text = value
  void _onControllerTextChanged() {
    if (!mounted) return;

    final currentText = widget.controller?.text ?? '';
    final hasContent = currentText.trim().isNotEmpty;

    // Clear validation error immediately when text becomes non-empty programmatically
    if (hasContent && currentError != null) {
      setState(() {
        currentError = null;
        // Also update typing state to reflect that field now has content
        _hasUserStartedTyping = true;
      });
    }

    // If text becomes empty programmatically, reset typing state
    if (!hasContent && _hasUserStartedTyping) {
      setState(() {
        _hasUserStartedTyping = false;
      });
    }
  }

  /// Legacy validation for backward compatibility
  void _performLegacyValidation(String value) {
    String? error;

    // Use custom validator if provided
    if (widget.validator != null) {
      error = widget.validator!(value);
    } else {
      // Default validation logic
      if (widget.isRequired && (value.trim().isEmpty)) {
        error = 'Please enter a valid ${widget.labelText ?? 'value'}';
      }
    }

    // Only update error state based on typing state
    final hasContent = value.trim().isNotEmpty;
    final shouldShowError =
        error != null && (!hasContent || !_hasUserStartedTyping);

    if (mounted) {
      setState(() {
        currentError = shouldShowError ? error : null;
      });
    }
  }

  /// Get border color based on validation state
  Color _getBorderColor() {
    if (widget.borderColor != null) return widget.borderColor!;

    switch (_validationState) {
      case ValidationState.invalid:
        return AppColors.red.withValues(alpha: 0.8);
      case ValidationState.valid:
        return widget.showValidationStateIndicator
            ? AppColors.green00.withValues(alpha: 0.8)
            : AppColors.secondary300;
      case ValidationState.validating:
        return AppColors.primaryPurple.withValues(alpha: 0.6);
      case ValidationState.initial:
        return AppColors.secondary300;
    }
  }

  /// Validate immediately (for form submission)
  Future<bool> validate() async {
    if (_validationManager != null) {
      final result =
          await _validationManager!.validateImmediate(widget.controller?.text);
      return result.isValid;
    } else {
      // Legacy validation
      final value = widget.controller?.text ?? '';
      _performLegacyValidation(value);
      return currentError == null;
    }
  }

  /// Get current validation state
  bool get isValid {
    if (_validationManager != null) {
      return _validationManager!.isValid;
    } else {
      return currentError == null;
    }
  }

  /// Clear validation state
  void clearValidation() {
    _validationManager?.clear();
    setState(() {
      currentError = null;
      _hasUserStartedTyping = false;
      _validationState = ValidationState.initial;
    });
  }

  @override
  Widget build(BuildContext context) {
    final borderColor = _getBorderColor();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showLabelHeader)
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Text(
                    widget.labelText ?? '',
                    style: TextStyle(
                      color: AppColors.neutral400,
                      fontSize: widget.labelSize ?? 16.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const XBox(4),
                  Text(
                    widget.optionalText ?? '',
                    style: TextStyle(
                      color: AppColors.neutral200,
                      fontSize: 12.sp,
                    ),
                  ),
                  // Validation state indicator
                  if (widget.showValidationStateIndicator &&
                      _validationState != ValidationState.initial)
                    Padding(
                      padding: EdgeInsets.only(left: 8.w),
                      child: _buildValidationIndicator(),
                    ),
                ],
              ),
              const YBox(4)
            ],
          ),
        SizedBox(
          width: widget.width ?? Sizer.screenWidth,
          child: TextField(
            // Changed from TextFormField to TextField
            enableInteractiveSelection: widget.enableInteractiveSelection,
            showCursor: widget.showCursor,
            maxLines: widget.maxLines ?? 1,
            textAlign: widget.textAlign,
            cursorHeight: 16.sp,
            cursorColor: AppColors.black,
            focusNode: widget.focusNode,
            style: TextStyle(
              color: widget.textfieldColor ?? AppColors.black,
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
            ),
            controller: widget.controller,
            obscureText: widget.isPassword! && !showPassword,
            keyboardType: widget.inputType ?? inputType(widget.keyboardType),
            onSubmitted: widget.onSubmitted,
            inputFormatters:
                widget.inputFormatters ?? inputFormatter(widget.keyboardType),
            onChanged: _handleTextChange,
            onTap: widget.onTap,
            readOnly: widget.isReadOnly!,
            decoration: InputDecoration(
              constraints: BoxConstraints(
                maxHeight: widget.maxLines != null
                    ? double.infinity
                    : widget.height ?? 50.h,
                minHeight: widget.maxLines != null ? 0 : widget.height ?? 50.h,
              ),
              contentPadding: widget.contentPadding ??
                  EdgeInsets.only(
                    top: 20.h,
                    bottom: 0.h,
                    left: 14.w,
                    right: 10.w,
                  ),
              hintText: widget.hintText,
              hintStyle: widget.hintStyle ??
                  TextStyle(
                    fontSize: Sizer.text(16),
                    fontWeight: FontWeight.w400,
                    color: AppColors.neutral100.withValues(alpha: 0.2),
                  ),
              suffixIcon: widget.suffixIcon ?? suffixIcon(),
              prefix: widget.prefix,
              prefixIcon: widget.prefixIcon,
              fillColor: widget.fillColor ?? AppColors.white,
              filled: widget.showfillColor ?? true,
              enabledBorder: OutlineInputBorder(
                borderSide: widget.hideBorder
                    ? BorderSide.none
                    : BorderSide(width: 1, color: borderColor),
                borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
              ),
              disabledBorder: OutlineInputBorder(
                borderSide: widget.hideBorder
                    ? BorderSide.none
                    : const BorderSide(width: 1, color: AppColors.secondary300),
                borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
              ),
              border: OutlineInputBorder(
                borderSide: widget.hideBorder
                    ? BorderSide.none
                    : BorderSide(width: 1, color: borderColor),
                borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: widget.hideBorder
                    ? BorderSide.none
                    : BorderSide(
                        width: 1, color: AppColors.red.withValues(alpha: 0.8)),
                borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderSide: widget.hideBorder
                    ? BorderSide.none
                    : BorderSide(
                        width: 1, color: AppColors.red.withValues(alpha: 0.8)),
                borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: widget.hideBorder
                    ? BorderSide.none
                    : BorderSide(
                        width: 1,
                        color: _validationState == ValidationState.invalid
                            ? AppColors.red.withValues(alpha: 0.8)
                            : AppColors.primaryPurple.withValues(alpha: 0.4),
                      ),
                borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
              ),
            ),
          ),
        ),

        // Show error text below the textfield
        if (currentError != null)
          Padding(
            padding: EdgeInsets.only(left: 4.w),
            child: Text(
              currentError!,
              style: TextStyle(
                color: AppColors.red.withValues(alpha: 0.8),
                fontSize: 12.sp,
              ),
            ),
          ),
      ],
    );
  }

  /// Build validation state indicator
  Widget _buildValidationIndicator() {
    switch (_validationState) {
      case ValidationState.validating:
        return SizedBox(
          width: 12.w,
          height: 12.h,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              AppColors.primaryPurple.withValues(alpha: 0.6),
            ),
          ),
        );
      case ValidationState.valid:
        return Icon(
          Icons.check_circle,
          size: 16.sp,
          color: AppColors.green00.withValues(alpha: 0.8),
        );
      case ValidationState.invalid:
        return Icon(
          Icons.error,
          size: 16.sp,
          color: AppColors.red.withValues(alpha: 0.8),
        );
      case ValidationState.initial:
        return const SizedBox.shrink();
    }
  }

  Widget? suffixIcon() {
    if (widget.isPassword! || widget.isConfirmPassword!) {
      return GestureDetector(
          onTap: () => setState(() {
                showPassword = !showPassword;
              }),
          child: PasswordSuffixWidget(
            showPassword: showPassword,
          ));
    }
    if (widget.showSuffixIcon! && widget.suffixIcon == null) {
      return const Icon(
        Iconsax.arrow_down,
        size: 18,
        color: AppColors.black,
      );
    }

    if (widget.showSuffixIcon! && widget.suffixIcon != null) {
      return widget.suffixIcon;
    }
    return null;
  }
}

class PasswordSuffixWidget extends StatelessWidget {
  final bool showPassword;
  const PasswordSuffixWidget({super.key, required this.showPassword});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50.w,
      alignment: Alignment.center,
      margin: EdgeInsets.only(
        top: 11.h,
        bottom: 11.h,
        right: 10.w,
      ),
      decoration: BoxDecoration(
        color: AppColors.accent50,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        showPassword ? 'Hide' : 'Show',
        style: AppTypography.text12.copyWith(
          color: AppColors.primaryPurple,
        ),
      ),
    );
  }
}
