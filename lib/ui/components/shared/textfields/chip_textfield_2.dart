import 'package:flutter/material.dart';

class ChipTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final Color? matchChipColor;
  final Function(List<String>)? onMatchesChanged;
  final String? Function(String?)? validator;
  final InputDecoration? decoration;
  final String? label;

  const ChipTextField({
    super.key,
    this.controller,
    this.hintText,
    this.matchChipColor,
    this.onMatchesChanged,
    this.validator,
    this.decoration,
    this.label,
  });

  @override
  State<ChipTextField> createState() => _ChipTextFieldState();
}

class _ChipTextFieldState extends State<ChipTextField> {
  final TextEditingController _controller = TextEditingController();
  final List<String> _matches = [];
  String _currentText = '';
  final FocusNode _focusNode = FocusNode();
  String? _errorText;

  // Patterns to validate emails and phone numbers
  final RegExp _emailPattern =
      RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
  final RegExp _phonePattern = RegExp(r'^\d{10,12}$');

  TextEditingController get _effectiveController =>
      widget.controller ?? _controller;

  @override
  void initState() {
    super.initState();
    _effectiveController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _effectiveController.removeListener(_onTextChanged);
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _effectiveController.text;
    if (text.endsWith(' ')) {
      final trimmedText = text.trim();
      if (trimmedText.isNotEmpty) {
        _processText(trimmedText);
      }
    } else {
      setState(() {
        _currentText = text;
        _validateCurrentText();
      });
    }
  }

  void _validateCurrentText() {
    if (_currentText.isNotEmpty && !_isValidMatch(_currentText)) {
      _errorText = 'Invalid email or phone number';
    } else {
      _errorText = null;
    }
  }

  bool _isValidMatch(String text) {
    return _emailPattern.hasMatch(text) || _phonePattern.hasMatch(text);
  }

  void _processText(String text) {
    if (_isValidMatch(text)) {
      setState(() {
        _matches.add(text);
        _effectiveController.clear();
        _currentText = '';
        _errorText = null;
      });
      widget.onMatchesChanged?.call(_matches);
    } else {
      setState(() {
        _errorText = 'Invalid email or phone number';
        _effectiveController.text = text;
        _effectiveController.selection = TextSelection.fromPosition(
          TextPosition(offset: text.length),
        );
      });
    }
  }

  void _removeMatch(String match) {
    setState(() {
      _matches.remove(match);
    });
    widget.onMatchesChanged?.call(_matches);
  }

  String _getChipLabel(String match) {
    if (_emailPattern.hasMatch(match)) {
      // Get the local part of the email (before @)
      final localPart = match.split('@')[0];
      if (localPart.length > 10) {
        return '${localPart.substring(0, 10)}...';
      }
      return localPart;
    } else {
      // For phone numbers
      if (match.length > 10) {
        return '${match.substring(0, 10)}...';
      }
      return match;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.label!,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 8, // Space between chips
                runSpacing: 8, // Space between lines
                children: _matches.map((match) => _buildChip(match)).toList(),
              ),
              TextField(
                controller: _effectiveController,
                focusNode: _focusNode,
                decoration: (widget.decoration ??
                        InputDecoration(
                          hintText:
                              widget.hintText ?? 'Enter email or phone number',
                          border: InputBorder.none,
                          errorText: _errorText,
                          isDense: true,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 8),
                        ))
                    .copyWith(errorText: _errorText),
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    _processText(value.trim());
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildChip(String match) {
    // Design matching the screenshot with pill-shaped chip and X button
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: widget.matchChipColor ?? Colors.grey.shade200,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            match, // Using full text as in the screenshot
            style: const TextStyle(
              fontSize: 14,
            ),
          ),
          const SizedBox(width: 4),
          InkWell(
            onTap: () => _removeMatch(match),
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.grey.shade400,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 14,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Example implementation matching the design in the screenshot
class InviteFieldExample extends StatefulWidget {
  const InviteFieldExample({super.key});

  @override
  State<InviteFieldExample> createState() => _InviteFieldExampleState();
}

class _InviteFieldExampleState extends State<InviteFieldExample> {
  List<String> _invitees = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {},
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Duration field
            const Text(
              "Duration",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: "",
                      ),
                    ),
                  ),
                  Icon(Icons.arrow_drop_down, color: Colors.grey.shade600),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Commencement Date field
            const Text(
              "Commencement Date",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: "DD/MM/YY",
                      ),
                    ),
                  ),
                  Icon(Icons.calendar_today, color: Colors.grey.shade600),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Number of Stakeholders field
            const Text(
              "Number of Stakeholders (You included)",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: "You +",
                      ),
                    ),
                  ),
                  Icon(Icons.arrow_drop_down, color: Colors.grey.shade600),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Invite by phone number or email field - our custom widget
            ChipTextField(
              label: "Invite by phone number or email",
              hintText: "",
              matchChipColor: Colors.grey.shade200,
              onMatchesChanged: (matches) {
                setState(() {
                  _invitees = matches;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
