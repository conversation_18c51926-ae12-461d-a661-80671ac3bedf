import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class CustomPinWidget extends StatelessWidget {
  const CustomPinWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.controller,
    this.pinFocusNode,
    this.onChanged,
    this.onCompleted,
    this.validator,
    this.pinputAutovalidateMode = PinputAutovalidateMode.onSubmit,
  });

  final String title;
  final String? subtitle;
  final TextEditingController? controller;
  final FocusNode? pinFocusNode;
  final Function(String)? onChanged;
  final Function(String)? onCompleted;
  final String? Function(String?)? validator;
  final PinputAutovalidateMode pinputAutovalidateMode;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(Sizer.radius(10)),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(100),
              ),
              child: svgHelper(
                AppSvgs.shield,
                height: Sizer.height(60),
                width: Sizer.width(60),
              ),
            ),
          ],
        ),
        const YBox(16),
        CustomSubHeader(
          title: title,
          subtitle: subtitle,
        ),
        const YBox(32),
        Pinput(
          pinputAutovalidateMode: pinputAutovalidateMode,
          defaultPinTheme: PinInputTheme.defaultPinTheme(),
          followingPinTheme: PinInputTheme.followPinTheme(),
          length: 4,
          controller: controller,
          focusNode: pinFocusNode,
          showCursor: true,
          onChanged: onChanged,
          onCompleted: onCompleted,
          validator: validator,
        ),
      ],
    );
  }
}
