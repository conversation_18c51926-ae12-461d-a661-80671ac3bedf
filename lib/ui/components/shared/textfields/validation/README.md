# CustomTextField Validation System

A comprehensive, flexible validation system for the CustomTextField component that replaces Flutter's built-in TextFormField validation mechanism.

## Features

- ✅ **Independent Validation**: No dependency on <PERSON><PERSON><PERSON>'s Form widget
- ✅ **Real-time Validation**: Validates on text change with debouncing
- ✅ **Multiple Rule Support**: Apply multiple validation rules per field
- ✅ **Priority-based Validation**: Rules are executed in priority order
- ✅ **Async Validation**: Support for server-side validation
- ✅ **Visual Feedback**: Border colors and validation state indicators
- ✅ **Error Clearing**: Smart error clearing when user starts typing
- ✅ **Backward Compatible**: Works with existing CustomTextField usage
- ✅ **Debounced Validation**: Prevents excessive validation during rapid typing

## Quick Start

### Basic Usage

```dart
CustomTextField(
  labelText: 'Email',
  validationRules: ValidationRules.requiredEmail(),
  enableRealTimeValidation: true,
  showValidationStateIndicator: true,
)
```

### Advanced Usage

```dart
CustomTextField(
  labelText: 'Password',
  isPassword: true,
  validationRules: ValidationRules.password(
    minLength: 8,
    requireUppercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
  ),
  validationDebounceDelay: Duration(milliseconds: 500),
  onValidationChanged: (result) {
    print('Validation state: ${result.state}');
    if (result.error != null) {
      print('Error: ${result.error}');
    }
  },
)
```

## Validation Rules

### Built-in Rules

#### RequiredRule
```dart
RequiredRule(
  fieldName: 'email',
  customErrorMessage: 'Email is required',
)
```

#### EmailRule
```dart
EmailRule(
  customErrorMessage: 'Please enter a valid email address',
)
```

#### PhoneRule
```dart
PhoneRule(
  minLength: 11,
  customErrorMessage: 'Phone number must be at least 11 digits',
)
```

#### MinLengthRule / MaxLengthRule
```dart
MinLengthRule(minLength: 8),
MaxLengthRule(maxLength: 50),
```

#### RegexRule
```dart
RegexRule(
  regex: RegExp(r'^[a-zA-Z0-9]+$'),
  ruleName: 'alphanumeric',
  customErrorMessage: 'Only letters and numbers allowed',
)
```

#### CustomRule
```dart
CustomRule(
  validator: (value) {
    if (value?.contains('admin') == true) {
      return 'Cannot contain "admin"';
    }
    return null;
  },
)
```

#### AsyncRule
```dart
AsyncRule(
  asyncValidator: (value) async {
    // Simulate server call
    await Future.delayed(Duration(seconds: 1));
    
    if (await isUsernameTaken(value)) {
      return 'Username is already taken';
    }
    return null;
  },
)
```

### Pre-built Rule Combinations

#### Required Email
```dart
ValidationRules.requiredEmail()
```

#### Required Phone
```dart
ValidationRules.requiredPhone(minLength: 11)
```

#### Password with Requirements
```dart
ValidationRules.password(
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
)
```

#### Required Field with Length Constraints
```dart
ValidationRules.requiredWithLength(
  fieldName: 'username',
  minLength: 3,
  maxLength: 20,
)
```

## Parameters

### New Validation Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `validationRules` | `List<ValidationRule>?` | `null` | List of validation rules to apply |
| `validationDebounceDelay` | `Duration?` | `300ms` | Delay before validation triggers |
| `onValidationChanged` | `Function(ValidationResult)?` | `null` | Callback for validation state changes |
| `enableRealTimeValidation` | `bool` | `true` | Enable real-time validation |
| `showValidationStateIndicator` | `bool` | `false` | Show visual validation indicators |

### Legacy Parameters (Deprecated)

| Parameter | Type | Description |
|-----------|------|-------------|
| `validator` | `String? Function(String?)?` | Use `validationRules` instead |
| `isRequired` | `bool` | Use `RequiredRule` instead |

## Validation States

The validation system uses four states:

- **`ValidationState.initial`**: No validation performed yet
- **`ValidationState.validating`**: Async validation in progress
- **`ValidationState.valid`**: All validations passed
- **`ValidationState.invalid`**: One or more validations failed

## Real-time Error Clearing

The system implements smart error clearing:

1. **Errors persist** when field is empty and unfocused
2. **Errors clear immediately** when user starts typing valid content
3. **Errors reappear** if field becomes empty again (for required fields)
4. **No errors shown** while actively typing (prevents UI flickering)

## Form Integration

### Individual Field Validation

```dart
class MyForm extends StatefulWidget {
  @override
  _MyFormState createState() => _MyFormState();
}

class _MyFormState extends State<MyForm> {
  bool _isFormValid = false;
  final Map<String, bool> _fieldStates = {};

  void _updateFormValidation(String fieldName, bool isValid) {
    setState(() {
      _fieldStates[fieldName] = isValid;
      _isFormValid = _fieldStates.values.every((valid) => valid);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomTextField(
          labelText: 'Email',
          validationRules: ValidationRules.requiredEmail(),
          onValidationChanged: (result) => 
            _updateFormValidation('email', result.isValid),
        ),
        // ... more fields
        ElevatedButton(
          onPressed: _isFormValid ? _submitForm : null,
          child: Text('Submit'),
        ),
      ],
    );
  }
}
```

### Programmatic Validation

```dart
// Access validation methods through GlobalKey
final _fieldKey = GlobalKey<_CustomTextFieldState>();

CustomTextField(
  key: _fieldKey,
  validationRules: ValidationRules.requiredEmail(),
)

// Validate programmatically
bool isValid = await _fieldKey.currentState?.validate() ?? false;

// Clear validation
_fieldKey.currentState?.clearValidation();

// Check current state
bool currentlyValid = _fieldKey.currentState?.isValid ?? false;
```

## Migration Guide

### From Legacy Validation

**Before:**
```dart
CustomTextField(
  validator: (value) {
    if (value?.isEmpty == true) return 'Required';
    if (!value!.contains('@')) return 'Invalid email';
    return null;
  },
  isRequired: true,
)
```

**After:**
```dart
CustomTextField(
  validationRules: ValidationRules.requiredEmail(),
)
```

### Custom Validation Migration

**Before:**
```dart
CustomTextField(
  validator: (value) {
    if (value?.isEmpty == true) return 'Username required';
    if (value!.length < 3) return 'Too short';
    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
      return 'Alphanumeric only';
    }
    return null;
  },
)
```

**After:**
```dart
CustomTextField(
  validationRules: [
    RequiredRule(fieldName: 'username'),
    MinLengthRule(minLength: 3),
    RegexRule(
      regex: RegExp(r'^[a-zA-Z0-9]+$'),
      ruleName: 'alphanumeric username',
    ),
  ],
)
```

## Best Practices

1. **Use pre-built rule combinations** when possible
2. **Set appropriate debounce delays** for async validation
3. **Handle validation state changes** in parent widgets for form coordination
4. **Provide clear error messages** with custom messages
5. **Order rules by priority** (required rules first, format rules last)
6. **Use async validation sparingly** to avoid performance issues

## Performance Considerations

- Validation is debounced to prevent excessive calls
- Synchronous rules are executed before async rules
- Validation stops at the first failing rule
- State updates are batched to minimize rebuilds
