import 'dart:async';

import 'validation_rule.dart';

/// Validation result containing state and error information
class ValidationResult {
  final ValidationState state;
  final String? error;
  final bool isValid;

  const ValidationResult({
    required this.state,
    this.error,
  }) : isValid = state == ValidationState.valid;

  const ValidationResult.initial() : this(state: ValidationState.initial);
  const ValidationResult.validating() : this(state: ValidationState.validating);
  const ValidationResult.valid() : this(state: ValidationState.valid);
  const ValidationResult.invalid(String error)
      : this(state: ValidationState.invalid, error: error);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ValidationResult &&
          runtimeType == other.runtimeType &&
          state == other.state &&
          error == other.error;

  @override
  int get hashCode => state.hashCode ^ error.hashCode;
}

/// Manages validation for a single field
class ValidationManager {
  final List<ValidationRule> _rules;
  final Duration debounceDelay;
  final void Function(ValidationResult)? onValidationChanged;

  Timer? _debounceTimer;
  ValidationResult _currentResult = const ValidationResult.initial();
  String? _lastValidatedValue;

  ValidationManager({
    required List<ValidationRule> rules,
    this.debounceDelay = const Duration(milliseconds: 300),
    this.onValidationChanged,
  }) : _rules = List.from(rules)
          ..sort((a, b) => b.priority.compareTo(a.priority));

  /// Current validation result
  ValidationResult get currentResult => _currentResult;

  /// Whether the field is currently valid
  bool get isValid => _currentResult.isValid;

  /// Whether validation is in progress
  bool get isValidating => _currentResult.state == ValidationState.validating;

  /// Current error message
  String? get error => _currentResult.error;

  /// Validates the value with debouncing
  void validateWithDebounce(String? value) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(debounceDelay, () {
      validate(value);
    });
  }

  /// Validates the value immediately
  Future<ValidationResult> validate(String? value) async {
    // Skip validation if value hasn't changed
    if (value == _lastValidatedValue &&
        _currentResult.state != ValidationState.initial) {
      return _currentResult;
    }

    _lastValidatedValue = value;

    // Set validating state for async rules
    final hasAsyncRules = _rules.any((rule) => rule.isAsync);
    if (hasAsyncRules) {
      _updateResult(const ValidationResult.validating());
    }

    try {
      // Run synchronous validations first
      for (final rule in _rules.where((r) => !r.isAsync)) {
        final error = rule.validateSync(value);
        if (error != null) {
          final result = ValidationResult.invalid(error);
          _updateResult(result);
          return result;
        }
      }

      // Run asynchronous validations
      for (final rule in _rules.where((r) => r.isAsync)) {
        final error = await rule.validate(value);
        if (error != null) {
          final result = ValidationResult.invalid(error);
          _updateResult(result);
          return result;
        }
      }

      // All validations passed
      const result = ValidationResult.valid();
      _updateResult(result);
      return result;
    } catch (e) {
      const result = ValidationResult.invalid('Validation error occurred');
      _updateResult(result);
      return result;
    }
  }

  /// Validates immediately without debouncing (for form submission)
  Future<ValidationResult> validateImmediate(String? value) async {
    _debounceTimer?.cancel();
    return await validate(value);
  }

  /// Clears the current validation state
  void clear() {
    _debounceTimer?.cancel();
    _lastValidatedValue = null;
    _updateResult(const ValidationResult.initial());
  }

  /// Resets validation state to initial
  void reset() {
    clear();
  }

  /// Updates the validation result and notifies listeners
  void _updateResult(ValidationResult result) {
    if (_currentResult != result) {
      _currentResult = result;
      onValidationChanged?.call(result);
    }
  }

  /// Disposes resources
  void dispose() {
    _debounceTimer?.cancel();
  }
}

/// Factory class for creating common validation rule combinations
class ValidationRules {
  ValidationRules._();

  /// Creates rules for a required email field
  static List<ValidationRule> requiredEmail({
    String fieldName = 'email',
    String? requiredMessage,
    String? emailMessage,
  }) {
    return [
      RequiredRule(
        fieldName: fieldName,
        customErrorMessage: requiredMessage,
      ),
      EmailRule(
        customErrorMessage: emailMessage,
      ),
    ];
  }

  /// Creates rules for a required phone field
  static List<ValidationRule> requiredPhone({
    String fieldName = 'phone number',
    int minLength = 11,
    String? requiredMessage,
    String? phoneMessage,
  }) {
    return [
      RequiredRule(
        fieldName: fieldName,
        customErrorMessage: requiredMessage,
      ),
      PhoneRule(
        minLength: minLength,
        customErrorMessage: phoneMessage,
      ),
    ];
  }

  /// Creates rules for a required field with min/max length
  static List<ValidationRule> requiredWithLength({
    required String fieldName,
    int? minLength,
    int? maxLength,
    String? requiredMessage,
    String? minLengthMessage,
    String? maxLengthMessage,
  }) {
    final rules = <ValidationRule>[
      RequiredRule(
        fieldName: fieldName,
        customErrorMessage: requiredMessage,
      ),
    ];

    if (minLength != null) {
      rules.add(MinLengthRule(
        minLength: minLength,
        customErrorMessage: minLengthMessage,
      ));
    }

    if (maxLength != null) {
      rules.add(MaxLengthRule(
        maxLength: maxLength,
        customErrorMessage: maxLengthMessage,
      ));
    }

    return rules;
  }

  /// Creates rules for an optional email field
  static List<ValidationRule> optionalEmail({
    String? emailMessage,
  }) {
    return [
      EmailRule(
        customErrorMessage: emailMessage,
      ),
    ];
  }

  /// Creates rules for password field
  static List<ValidationRule> password({
    String fieldName = 'password',
    int minLength = 8,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumbers = true,
    bool requireSpecialChars = false,
  }) {
    final rules = <ValidationRule>[
      RequiredRule(fieldName: fieldName),
      MinLengthRule(minLength: minLength),
    ];

    if (requireUppercase) {
      rules.add(RegexRule(
        regex: RegExp(r'[A-Z]'),
        ruleName: 'uppercase letter',
        customErrorMessage:
            'Password must contain at least one uppercase letter',
      ));
    }

    if (requireLowercase) {
      rules.add(RegexRule(
        regex: RegExp(r'[a-z]'),
        ruleName: 'lowercase letter',
        customErrorMessage:
            'Password must contain at least one lowercase letter',
      ));
    }

    if (requireNumbers) {
      rules.add(RegexRule(
        regex: RegExp(r'[0-9]'),
        ruleName: 'number',
        customErrorMessage: 'Password must contain at least one number',
      ));
    }

    if (requireSpecialChars) {
      rules.add(RegexRule(
        regex: RegExp(r'[!@#$%^&*(),.?":{}|<>]'),
        ruleName: 'special character',
        customErrorMessage:
            'Password must contain at least one special character',
      ));
    }

    return rules;
  }
}
