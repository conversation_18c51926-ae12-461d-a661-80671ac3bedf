import 'dart:async';

import 'package:equalcash/ui/components/shared/textfields/validation/form_validator.dart';
import 'package:flutter/material.dart';

/// Interface for form validation operations
abstract class FormValidatorController {
  /// Validates all CustomTextField widgets within this form.
  Future<bool> validateForm();

  /// Clears validation state for all CustomTextField widgets within this form.
  void clearValidation();

  /// Checks if all CustomTextField widgets within this form are currently valid.
  bool get isFormCurrentlyValid;
}

/// A widget that wraps forms and provides validation capabilities.
///
/// This widget automatically manages validation for all CustomTextField widgets
/// within its child widget tree and provides callbacks for validation events.
///
/// Usage:
/// ```dart
/// FormValidatorWidget(
///   onValidationChanged: (isValid) {
///     setState(() {
///       _isFormValid = isValid;
///     });
///   },
///   child: Column(
///     children: [
///       CustomTextField(
///         labelText: 'Email',
///         validationRules: ValidationRules.requiredEmail(),
///       ),
///       CustomTextField(
///         labelText: 'Password',
///         validationRules: ValidationRules.password(),
///       ),
///     ],
///   ),
/// )
/// ```
class FormValidatorWidget extends StatefulWidget {
  /// The child widget containing the form fields
  final Widget child;

  /// Callback called when the overall form validation state changes
  final void Function(bool isValid)? onValidationChanged;

  /// Whether to automatically validate the form when field values change
  final bool autoValidate;

  /// Debounce duration for auto-validation to avoid excessive validation calls
  final Duration autoValidateDebounce;

  const FormValidatorWidget({
    super.key,
    required this.child,
    this.onValidationChanged,
    this.autoValidate = false,
    this.autoValidateDebounce = const Duration(milliseconds: 500),
  });

  @override
  State<FormValidatorWidget> createState() => _FormValidatorWidgetState();
}

class _FormValidatorWidgetState extends State<FormValidatorWidget>
    implements FormValidatorController {
  bool _isFormValid = false;
  Timer? _autoValidateTimer;

  /// Validates all CustomTextField widgets within this form.
  ///
  /// Returns true if all fields are valid, false otherwise.
  @override
  Future<bool> validateForm() async {
    final isValid = await FormValidator.validateAllFields(context);
    _updateValidationState(isValid);
    return isValid;
  }

  /// Clears validation state for all CustomTextField widgets within this form.
  @override
  void clearValidation() {
    FormValidator.clearAllValidation(context);
    _updateValidationState(false);
  }

  /// Checks if all CustomTextField widgets within this form are currently valid.
  ///
  /// This method does not trigger validation, it only checks the current state.
  @override
  bool get isFormCurrentlyValid {
    return FormValidator.areAllFieldsValid(context);
  }

  /// Updates the form validation state and notifies listeners
  void _updateValidationState(bool isValid) {
    if (_isFormValid != isValid) {
      setState(() {
        _isFormValid = isValid;
      });
      widget.onValidationChanged?.call(isValid);
    }
  }

  /// Triggers auto-validation with debouncing
  void _triggerAutoValidation() {
    if (!widget.autoValidate) return;

    _autoValidateTimer?.cancel();
    _autoValidateTimer = Timer(widget.autoValidateDebounce, () {
      if (mounted) {
        final isValid = isFormCurrentlyValid;
        _updateValidationState(isValid);
      }
    });
  }

  @override
  void dispose() {
    _autoValidateTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FormValidatorScope(
      formValidator: this,
      child: widget.autoValidate
          ? NotificationListener<ValidationChangeNotification>(
              onNotification: (notification) {
                _triggerAutoValidation();
                return true;
              },
              child: widget.child,
            )
          : widget.child,
    );
  }
}

/// An inherited widget that provides access to the FormValidatorWidget's methods
/// from anywhere within the widget tree.
///
/// This allows child widgets to access form validation methods without
/// requiring direct references or callbacks.
class FormValidatorScope extends InheritedWidget {
  final FormValidatorController formValidator;

  const FormValidatorScope({
    super.key,
    required this.formValidator,
    required super.child,
  });

  /// Gets the nearest FormValidatorScope from the widget tree.
  ///
  /// Returns null if no FormValidatorScope is found.
  static FormValidatorScope? maybeOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<FormValidatorScope>();
  }

  /// Gets the nearest FormValidatorScope from the widget tree.
  ///
  /// Throws an exception if no FormValidatorScope is found.
  static FormValidatorScope of(BuildContext context) {
    final scope = maybeOf(context);
    if (scope == null) {
      throw FlutterError(
        'FormValidatorScope.of() called with a context that does not contain a FormValidatorWidget.\n'
        'Make sure that the context is within a FormValidatorWidget.',
      );
    }
    return scope;
  }

  /// Validates all CustomTextField widgets within the form.
  Future<bool> validateForm() => formValidator.validateForm();

  /// Clears validation state for all CustomTextField widgets within the form.
  void clearValidation() => formValidator.clearValidation();

  /// Checks if all CustomTextField widgets within the form are currently valid.
  bool get isFormCurrentlyValid => formValidator.isFormCurrentlyValid;

  @override
  bool updateShouldNotify(FormValidatorScope oldWidget) {
    return formValidator != oldWidget.formValidator;
  }
}

/// A notification that can be dispatched when validation state changes.
///
/// This is used internally by the FormValidatorWidget for auto-validation.
class ValidationChangeNotification extends Notification {
  final bool isValid;

  const ValidationChangeNotification(this.isValid);
}

/// Extension methods on BuildContext for easy access to form validation.
///
/// These methods provide a convenient way to access form validation
/// functionality from any widget within a FormValidatorWidget.
///
/// Usage:
/// ```dart
/// // Validate the form
/// final isValid = await context.validateForm();
///
/// // Clear form validation
/// context.clearFormValidation();
///
/// // Check current validation state
/// final isCurrentlyValid = context.isFormCurrentlyValid;
/// ```
extension FormValidationContext on BuildContext {
  /// Validates all CustomTextField widgets within the nearest FormValidatorWidget.
  ///
  /// Returns true if all fields are valid, false otherwise.
  /// Throws an exception if no FormValidatorWidget is found in the widget tree.
  Future<bool> validateForm() async {
    return await FormValidatorScope.of(this).validateForm();
  }

  /// Clears validation state for all CustomTextField widgets within the nearest FormValidatorWidget.
  ///
  /// Throws an exception if no FormValidatorWidget is found in the widget tree.
  void clearFormValidation() {
    FormValidatorScope.of(this).clearValidation();
  }

  /// Checks if all CustomTextField widgets within the nearest FormValidatorWidget are currently valid.
  ///
  /// This method does not trigger validation, it only checks the current state.
  /// Throws an exception if no FormValidatorWidget is found in the widget tree.
  bool get isFormCurrentlyValid {
    return FormValidatorScope.of(this).isFormCurrentlyValid;
  }

  /// Tries to validate all CustomTextField widgets within the nearest FormValidatorWidget.
  ///
  /// Returns true if all fields are valid, false otherwise.
  /// Returns null if no FormValidatorWidget is found in the widget tree.
  Future<bool?> tryValidateForm() async {
    final scope = FormValidatorScope.maybeOf(this);
    return scope != null ? await scope.validateForm() : null;
  }

  /// Tries to clear validation state for all CustomTextField widgets within the nearest FormValidatorWidget.
  ///
  /// Does nothing if no FormValidatorWidget is found in the widget tree.
  void tryClearFormValidation() {
    FormValidatorScope.maybeOf(this)?.clearValidation();
  }

  /// Tries to check if all CustomTextField widgets within the nearest FormValidatorWidget are currently valid.
  ///
  /// Returns null if no FormValidatorWidget is found in the widget tree.
  bool? get tryIsFormCurrentlyValid {
    return FormValidatorScope.maybeOf(this)?.isFormCurrentlyValid;
  }
}
