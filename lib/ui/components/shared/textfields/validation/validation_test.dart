import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// Simple test screen to verify the validation system works
class ValidationTestScreen extends StatefulWidget {
  const ValidationTestScreen({super.key});

  @override
  State<ValidationTestScreen> createState() => _ValidationTestScreenState();
}

class _ValidationTestScreenState extends State<ValidationTestScreen> {
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  
  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Validation Test'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Test 1: Email validation
            CustomTextField(
              controller: _emailController,
              labelText: 'Email Address',
              hintText: 'Enter your email',
              keyboardType: KeyboardType.email,
              validationRules: const [
                RequiredRule(fieldName: 'email'),
                EmailRule(),
              ],
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
            ),
            const YBox(24),
            
            // Test 2: Phone validation
            CustomTextField(
              controller: _phoneController,
              labelText: 'Phone Number',
              hintText: 'Enter phone number',
              keyboardType: KeyboardType.phone,
              validationRules: const [
                RequiredRule(fieldName: 'phone number'),
                PhoneRule(minLength: 11),
              ],
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
            ),
            const YBox(24),
            
            // Test 3: Legacy validation (backward compatibility)
            CustomTextField(
              labelText: 'Legacy Field',
              hintText: 'Using old validation',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'This field is required';
                }
                return null;
              },
              isRequired: true,
            ),
            const YBox(32),
            
            CustomBtn.solid(
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Validation system is working!'),
                  ),
                );
              },
              text: 'Test Complete',
            ),
          ],
        ),
      ),
    );
  }
}
