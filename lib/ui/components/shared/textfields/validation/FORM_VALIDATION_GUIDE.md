# Reusable Form Validation System

This guide explains how to use the new reusable form validation system that automatically discovers and validates all `CustomTextField` widgets without requiring manual global key management.

## Overview

The system provides four different approaches for form validation:

1. **FormValidator Utility Class** - Direct utility methods
2. **FormValidationMixin** - Mixin for StatefulWidget states
3. **FormValidatorWidget** - Widget wrapper with auto-validation
4. **Context Extensions** - Convenient context-based methods

## Quick Start

### Method 1: FormValidationMixin (Recommended)

The easiest way to add form validation to any screen:

```dart
class MyFormScreen extends StatefulWidget {
  @override
  State<MyFormScreen> createState() => _MyFormScreenState();
}

class _MyFormScreenState extends State<MyFormScreen> with FormValidationMixin {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          CustomTextField(
            controller: _emailController,
            labelText: 'Email',
            validationRules: ValidationRules.requiredEmail(),
          ),
          CustomTextField(
            controller: _passwordController,
            labelText: 'Password',
            validationRules: ValidationRules.password(),
          ),
          ElevatedButton(
            onPressed: () async {
              if (await validateForm()) {
                // Form is valid, proceed
              }
            },
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }
}
```

### Method 2: FormValidator Utility

For direct control without mixins:

```dart
// Validate all fields
final isValid = await FormValidator.validateAllFields(context);

// Clear all validation
FormValidator.clearAllValidation(context);

// Check current state without triggering validation
final currentlyValid = FormValidator.areAllFieldsValid(context);
```

### Method 3: FormValidatorWidget

For automatic validation with callbacks:

```dart
FormValidatorWidget(
  autoValidate: true,
  onValidationChanged: (isValid) {
    setState(() {
      _isFormValid = isValid;
    });
  },
  child: Column(
    children: [
      CustomTextField(
        labelText: 'Email',
        validationRules: ValidationRules.requiredEmail(),
      ),
      // ... more fields
    ],
  ),
)
```

### Method 4: Context Extensions

When using FormValidatorWidget, you can access validation through context:

```dart
// Validate form
await context.validateForm();

// Clear validation
context.clearFormValidation();

// Check current state
final isValid = context.isFormCurrentlyValid;
```

## Migration from Manual Global Keys

### Before (Manual Approach)
```dart
class _MyFormState extends State<MyForm> {
  final GlobalKey<State<StatefulWidget>> _emailFieldKey = GlobalKey();
  final GlobalKey<State<StatefulWidget>> _passwordFieldKey = GlobalKey();

  Future<bool> _validateAllFields() async {
    final List<Future<bool>> validationFutures = [];
    
    final emailState = _emailFieldKey.currentState as dynamic;
    if (emailState?.validate != null) {
      validationFutures.add(emailState.validate());
    }
    
    final passwordState = _passwordFieldKey.currentState as dynamic;
    if (passwordState?.validate != null) {
      validationFutures.add(passwordState.validate());
    }
    
    final results = await Future.wait(validationFutures);
    return results.every((isValid) => isValid);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomTextField(
          key: _emailFieldKey,
          labelText: 'Email',
          validationRules: ValidationRules.requiredEmail(),
        ),
        CustomTextField(
          key: _passwordFieldKey,
          labelText: 'Password',
          validationRules: ValidationRules.password(),
        ),
        ElevatedButton(
          onPressed: () async {
            if (await _validateAllFields()) {
              // Proceed
            }
          },
          child: Text('Submit'),
        ),
      ],
    );
  }
}
```

### After (Using FormValidationMixin)
```dart
class _MyFormState extends State<MyForm> with FormValidationMixin {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomTextField(
          labelText: 'Email',
          validationRules: ValidationRules.requiredEmail(),
        ),
        CustomTextField(
          labelText: 'Password',
          validationRules: ValidationRules.password(),
        ),
        ElevatedButton(
          onPressed: () async {
            if (await validateForm()) {
              // Proceed
            }
          },
          child: Text('Submit'),
        ),
      ],
    );
  }
}
```

## Key Benefits

1. **No Manual Key Management** - Automatically discovers all CustomTextField widgets
2. **Reusable Across Screens** - Same code works on any screen
3. **Multiple Integration Options** - Choose the approach that fits your needs
4. **Backward Compatible** - Works with existing validation rules
5. **Type Safe** - Proper error handling for edge cases
6. **Performance Optimized** - Efficient widget tree traversal

## Advanced Usage

### Custom Validation Logic

You can still use custom validation rules with the new system:

```dart
CustomTextField(
  labelText: 'Username',
  validationRules: [
    RequiredRule(fieldName: 'username'),
    MinLengthRule(minLength: 3),
    CustomRule(
      validator: (value) {
        if (value?.contains(' ') == true) {
          return 'Username cannot contain spaces';
        }
        return null;
      },
    ),
  ],
)
```

### Conditional Validation

For forms with conditional fields:

```dart
CustomTextField(
  labelText: 'Optional Field',
  validationRules: _showOptionalField ? [
    RequiredRule(fieldName: 'optional field'),
  ] : null,
)
```

### Form State Management

With FormValidatorWidget, you can track overall form state:

```dart
bool _isFormValid = false;

FormValidatorWidget(
  autoValidate: true,
  onValidationChanged: (isValid) {
    setState(() {
      _isFormValid = isValid;
    });
  },
  child: Column(
    children: [
      // ... form fields
      ElevatedButton(
        onPressed: _isFormValid ? _submitForm : null,
        child: Text('Submit'),
      ),
    ],
  ),
)
```

## Best Practices

1. **Use FormValidationMixin** for most cases - it's the simplest approach
2. **Use FormValidatorWidget** when you need auto-validation or form state tracking
3. **Use FormValidator directly** when you need fine-grained control
4. **Always handle validation errors gracefully** - the system shows errors below fields automatically
5. **Test edge cases** - empty forms, network errors, etc.

## Error Handling

The system handles various edge cases:

- **No CustomTextField widgets found** - Returns true (valid)
- **Widget disposal during validation** - Safely ignores disposed widgets
- **Validation exceptions** - Treats as invalid and continues
- **Mixed validation states** - Only returns true if ALL fields are valid

## Performance Considerations

- **Widget tree traversal** is optimized and only runs when explicitly called
- **Validation debouncing** prevents excessive validation calls
- **Memory efficient** - no global key storage required
- **Lazy evaluation** - only validates when needed

## Examples

See `form_validation_examples.dart` for complete working examples of all four approaches.
