import 'package:equalcash/ui/components/shared/textfields/custom_textfield.dart';
import 'package:flutter/material.dart';

/// A utility class for automatically discovering and validating CustomTextField widgets
/// within a widget tree without requiring manual global key management.
class FormValidator {
  FormValidator._();

  /// Validates all CustomTextField widgets found in the given context.
  ///
  /// This method traverses the widget tree starting from the given context
  /// and finds all CustomTextField widgets, then triggers validation on each.
  ///
  /// Returns true if all fields are valid, false otherwise.
  ///
  /// Usage:
  /// ```dart
  /// final isValid = await FormValidator.validateAllFields(context);
  /// if (isValid) {
  ///   // Proceed with form submission
  /// } else {
  ///   // Show errors (already displayed below fields)
  /// }
  /// ```
  static Future<bool> validateAllFields(BuildContext context) async {
    final List<Future<bool>> validationFutures = [];

    // Find all CustomTextField widgets in the current context
    _findCustomTextFields(context, validationFutures);

    if (validationFutures.isEmpty) {
      return true; // No fields to validate
    }

    // Wait for all validations to complete
    final results = await Future.wait(validationFutures);

    // Return true only if all fields are valid
    return results.every((isValid) => isValid);
  }

  /// Clears validation state for all CustomTextField widgets in the given context.
  ///
  /// This is useful when you want to reset the form to its initial state.
  ///
  /// Usage:
  /// ```dart
  /// FormValidator.clearAllValidation(context);
  /// ```
  static void clearAllValidation(BuildContext context) {
    _findAndClearCustomTextFields(context);
  }

  /// Checks if all CustomTextField widgets in the given context are currently valid.
  ///
  /// This method does not trigger validation, it only checks the current state.
  ///
  /// Returns true if all fields are currently valid, false otherwise.
  ///
  /// Usage:
  /// ```dart
  /// final isCurrentlyValid = FormValidator.areAllFieldsValid(context);
  /// ```
  static bool areAllFieldsValid(BuildContext context) {
    final List<bool> validationStates = [];

    _findCustomTextFieldStates(context, validationStates);

    if (validationStates.isEmpty) {
      return true; // No fields to check
    }

    return validationStates.every((isValid) => isValid);
  }

  /// Recursively finds all CustomTextField widgets and adds their validation futures
  /// to the provided list.
  static void _findCustomTextFields(
      BuildContext context, List<Future<bool>> validationFutures) {
    void visitor(Element element) {
      final widget = element.widget;

      if (widget is CustomTextField) {
        // Found a CustomTextField, get its state and validate
        final state = element as StatefulElement;
        final customTextFieldState = state.state;

        // Use dynamic typing to access the validate method
        if (customTextFieldState.mounted) {
          try {
            final dynamic dynamicState = customTextFieldState;
            if (dynamicState.validate != null) {
              validationFutures.add(dynamicState.validate());
            }
          } catch (e) {
            // If validation fails, consider the field invalid
            validationFutures.add(Future.value(false));
          }
        }
      }

      // Continue traversing the widget tree
      element.visitChildren(visitor);
    }

    context.visitChildElements(visitor);
  }

  /// Recursively finds all CustomTextField widgets and clears their validation state.
  static void _findAndClearCustomTextFields(BuildContext context) {
    void visitor(Element element) {
      final widget = element.widget;

      if (widget is CustomTextField) {
        // Found a CustomTextField, get its state and clear validation
        final state = element as StatefulElement;
        final customTextFieldState = state.state;

        // Use dynamic typing to access the clearValidation method
        if (customTextFieldState.mounted) {
          try {
            final dynamic dynamicState = customTextFieldState;
            if (dynamicState.clearValidation != null) {
              dynamicState.clearValidation();
            }
          } catch (e) {
            // Ignore errors when clearing validation
          }
        }
      }

      // Continue traversing the widget tree
      element.visitChildren(visitor);
    }

    context.visitChildElements(visitor);
  }

  /// Recursively finds all CustomTextField widgets and checks their current validation state.
  static void _findCustomTextFieldStates(
      BuildContext context, List<bool> validationStates) {
    void visitor(Element element) {
      final widget = element.widget;

      if (widget is CustomTextField) {
        // Found a CustomTextField, get its state and check if valid
        final state = element as StatefulElement;
        final customTextFieldState = state.state;

        // Use dynamic typing to access the isValid getter
        if (customTextFieldState.mounted) {
          try {
            final dynamic dynamicState = customTextFieldState;
            if (dynamicState.isValid != null) {
              validationStates.add(dynamicState.isValid);
            } else {
              validationStates.add(false);
            }
          } catch (e) {
            // If checking state fails, consider the field invalid
            validationStates.add(false);
          }
        }
      }

      // Continue traversing the widget tree
      element.visitChildren(visitor);
    }

    context.visitChildElements(visitor);
  }
}

/// A mixin that provides form validation functionality to any StatefulWidget.
///
/// This mixin adds convenient methods for validating all CustomTextField widgets
/// within the widget's context without requiring manual global key management.
///
/// Usage:
/// ```dart
/// class MyFormScreen extends StatefulWidget {
///   @override
///   State<MyFormScreen> createState() => _MyFormScreenState();
/// }
///
/// class _MyFormScreenState extends State<MyFormScreen> with FormValidationMixin {
///   @override
///   Widget build(BuildContext context) {
///     return Scaffold(
///       body: Column(
///         children: [
///           CustomTextField(
///             labelText: 'Email',
///             validationRules: ValidationRules.requiredEmail(),
///           ),
///           CustomTextField(
///             labelText: 'Password',
///             validationRules: ValidationRules.password(),
///           ),
///           ElevatedButton(
///             onPressed: () async {
///               if (await validateForm()) {
///                 // Form is valid, proceed
///               }
///             },
///             child: Text('Submit'),
///           ),
///         ],
///       ),
///     );
///   }
/// }
/// ```
mixin FormValidationMixin<T extends StatefulWidget> on State<T> {
  /// Validates all CustomTextField widgets in this widget's context.
  ///
  /// Returns true if all fields are valid, false otherwise.
  Future<bool> validateForm() async {
    return await FormValidator.validateAllFields(context);
  }

  /// Clears validation state for all CustomTextField widgets in this widget's context.
  void clearFormValidation() {
    FormValidator.clearAllValidation(context);
  }

  /// Checks if all CustomTextField widgets in this widget's context are currently valid.
  ///
  /// This method does not trigger validation, it only checks the current state.
  bool get isFormCurrentlyValid {
    return FormValidator.areAllFieldsValid(context);
  }
}
