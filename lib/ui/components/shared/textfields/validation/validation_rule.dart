import 'dart:async';

/// Enum representing different validation states
enum ValidationState {
  initial,
  validating,
  valid,
  invalid,
}

/// Base class for all validation rules
abstract class ValidationRule {
  final String? customErrorMessage;
  final int priority;

  const ValidationRule({
    this.customErrorMessage,
    this.priority = 0,
  });

  /// Validates the input value and returns an error message if invalid
  /// Returns null if the value is valid
  Future<String?> validate(String? value);

  /// Synchronous validation for simple rules
  String? validateSync(String? value) => null;

  /// Whether this rule requires async validation
  bool get isAsync => false;
}

/// Required field validation rule
class RequiredRule extends ValidationRule {
  final String fieldName;

  const RequiredRule({
    required this.fieldName,
    super.customErrorMessage,
    super.priority = 100,
  });

  @override
  Future<String?> validate(String? value) async {
    return validateSync(value);
  }

  @override
  String? validateSync(String? value) {
    if (value == null || value.trim().isEmpty) {
      return customErrorMessage ?? 'Please enter a valid $fieldName';
    }
    return null;
  }
}

/// Email format validation rule
class EmailRule extends ValidationRule {
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  const EmailRule({
    super.customErrorMessage,
    super.priority = 50,
  });

  @override
  Future<String?> validate(String? value) async {
    return validateSync(value);
  }

  @override
  String? validateSync(String? value) {
    if (value == null || value.isEmpty) return null;
    
    if (!_emailRegex.hasMatch(value)) {
      return customErrorMessage ?? 'Please enter a valid email address';
    }
    return null;
  }
}

/// Phone number validation rule
class PhoneRule extends ValidationRule {
  final int minLength;
  static final RegExp _phoneRegex = RegExp(r'^\d+$');

  const PhoneRule({
    this.minLength = 11,
    super.customErrorMessage,
    super.priority = 50,
  });

  @override
  Future<String?> validate(String? value) async {
    return validateSync(value);
  }

  @override
  String? validateSync(String? value) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length < minLength) {
      return customErrorMessage ?? 'Phone number must be at least $minLength digits';
    }
    
    if (!_phoneRegex.hasMatch(value)) {
      return customErrorMessage ?? 'Phone number must contain only digits';
    }
    return null;
  }
}

/// Minimum length validation rule
class MinLengthRule extends ValidationRule {
  final int minLength;

  const MinLengthRule({
    required this.minLength,
    super.customErrorMessage,
    super.priority = 30,
  });

  @override
  Future<String?> validate(String? value) async {
    return validateSync(value);
  }

  @override
  String? validateSync(String? value) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length < minLength) {
      return customErrorMessage ?? 'Must be at least $minLength characters long';
    }
    return null;
  }
}

/// Maximum length validation rule
class MaxLengthRule extends ValidationRule {
  final int maxLength;

  const MaxLengthRule({
    required this.maxLength,
    super.customErrorMessage,
    super.priority = 30,
  });

  @override
  Future<String?> validate(String? value) async {
    return validateSync(value);
  }

  @override
  String? validateSync(String? value) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length > maxLength) {
      return customErrorMessage ?? 'Must be no more than $maxLength characters long';
    }
    return null;
  }
}

/// Custom regex validation rule
class RegexRule extends ValidationRule {
  final RegExp regex;
  final String ruleName;

  const RegexRule({
    required this.regex,
    required this.ruleName,
    super.customErrorMessage,
    super.priority = 20,
  });

  @override
  Future<String?> validate(String? value) async {
    return validateSync(value);
  }

  @override
  String? validateSync(String? value) {
    if (value == null || value.isEmpty) return null;
    
    if (!regex.hasMatch(value)) {
      return customErrorMessage ?? 'Please enter a valid $ruleName';
    }
    return null;
  }
}

/// Custom function validation rule
class CustomRule extends ValidationRule {
  final String? Function(String?) validator;

  const CustomRule({
    required this.validator,
    super.customErrorMessage,
    super.priority = 10,
  });

  @override
  Future<String?> validate(String? value) async {
    return validateSync(value);
  }

  @override
  String? validateSync(String? value) {
    return validator(value);
  }
}

/// Async validation rule for server-side validation
class AsyncRule extends ValidationRule {
  final Future<String?> Function(String?) asyncValidator;

  const AsyncRule({
    required this.asyncValidator,
    super.customErrorMessage,
    super.priority = 0,
  });

  @override
  bool get isAsync => true;

  @override
  Future<String?> validate(String? value) async {
    return await asyncValidator(value);
  }
}
