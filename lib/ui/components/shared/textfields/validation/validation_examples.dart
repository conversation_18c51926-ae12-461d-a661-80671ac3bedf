import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// Example usage of the new CustomTextField validation system
class ValidationExamples extends StatefulWidget {
  const ValidationExamples({super.key});

  @override
  State<ValidationExamples> createState() => _ValidationExamplesState();
}

class _ValidationExamplesState extends State<ValidationExamples> {
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();
  final _customController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    _customController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Validation Examples'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Example 1: Required Email Field
            Text(
              'Required Email Field',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              controller: _emailController,
              labelText: 'Email Address',
              hintText: 'Enter your email',
              keyboardType: KeyboardType.email,
              validationRules: ValidationRules.requiredEmail(),
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
              onValidationChanged: (result) {
                print('Email validation: ${result.state} - ${result.error}');
              },
            ),
            const YBox(24),

            // Example 2: Required Phone Field
            Text(
              'Required Phone Field',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              controller: _phoneController,
              labelText: 'Phone Number',
              hintText: 'Enter your phone number',
              keyboardType: KeyboardType.phone,
              validationRules: ValidationRules.requiredPhone(
                minLength: 11,
                phoneMessage: 'Please enter a valid phone number with at least 11 digits',
              ),
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
            ),
            const YBox(24),

            // Example 3: Password Field with Complex Rules
            Text(
              'Password Field',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              controller: _passwordController,
              labelText: 'Password',
              hintText: 'Enter your password',
              isPassword: true,
              validationRules: ValidationRules.password(
                minLength: 8,
                requireUppercase: true,
                requireLowercase: true,
                requireNumbers: true,
                requireSpecialChars: true,
              ),
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
              validationDebounceDelay: const Duration(milliseconds: 500),
            ),
            const YBox(24),

            // Example 4: Name Field with Length Constraints
            Text(
              'Name Field with Length Constraints',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              controller: _nameController,
              labelText: 'Full Name',
              hintText: 'Enter your full name',
              validationRules: ValidationRules.requiredWithLength(
                fieldName: 'full name',
                minLength: 2,
                maxLength: 50,
              ),
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
            ),
            const YBox(24),

            // Example 5: Custom Validation Rules
            Text(
              'Custom Validation Rules',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              controller: _customController,
              labelText: 'Username',
              hintText: 'Enter username (alphanumeric only)',
              validationRules: [
                RequiredRule(fieldName: 'username'),
                MinLengthRule(minLength: 3),
                MaxLengthRule(maxLength: 20),
                RegexRule(
                  regex: RegExp(r'^[a-zA-Z0-9]+$'),
                  ruleName: 'alphanumeric username',
                  customErrorMessage: 'Username can only contain letters and numbers',
                ),
                CustomRule(
                  validator: (value) {
                    if (value != null && value.toLowerCase().contains('admin')) {
                      return 'Username cannot contain "admin"';
                    }
                    return null;
                  },
                ),
              ],
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
            ),
            const YBox(24),

            // Example 6: Optional Email Field
            Text(
              'Optional Email Field',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              labelText: 'Secondary Email',
              hintText: 'Enter secondary email (optional)',
              keyboardType: KeyboardType.email,
              validationRules: ValidationRules.optionalEmail(),
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
            ),
            const YBox(24),

            // Example 7: Async Validation (Server-side check simulation)
            Text(
              'Async Validation Example',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              labelText: 'Username (Async Check)',
              hintText: 'Enter username to check availability',
              validationRules: [
                RequiredRule(fieldName: 'username'),
                MinLengthRule(minLength: 3),
                AsyncRule(
                  asyncValidator: (value) async {
                    if (value == null || value.isEmpty) return null;
                    
                    // Simulate server call
                    await Future.delayed(const Duration(seconds: 1));
                    
                    // Simulate some usernames being taken
                    final takenUsernames = ['admin', 'user', 'test', 'demo'];
                    if (takenUsernames.contains(value.toLowerCase())) {
                      return 'Username "$value" is already taken';
                    }
                    
                    return null;
                  },
                ),
              ],
              enableRealTimeValidation: true,
              showValidationStateIndicator: true,
              validationDebounceDelay: const Duration(milliseconds: 800),
            ),
            const YBox(32),

            // Example 8: Legacy Validation (Backward Compatibility)
            Text(
              'Legacy Validation (Backward Compatibility)',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              labelText: 'Legacy Field',
              hintText: 'Using old validation system',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'This field is required';
                }
                if (value.length < 5) {
                  return 'Must be at least 5 characters';
                }
                return null;
              },
              isRequired: true, // Legacy parameter
            ),
            const YBox(32),

            // Validation Summary Button
            CustomBtn.solid(
              onTap: () => _validateAllFields(),
              text: 'Validate All Fields',
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  void _validateAllFields() {
    // This would typically be done in a form submission context
    // For demonstration, we'll just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Check individual field validation states above'),
      ),
    );
  }
}

/// Example of how to create a form with coordinated validation
class ValidationFormExample extends StatefulWidget {
  const ValidationFormExample({super.key});

  @override
  State<ValidationFormExample> createState() => _ValidationFormExampleState();
}

class _ValidationFormExampleState extends State<ValidationFormExample> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isFormValid = false;
  final Map<String, bool> _fieldValidationStates = {};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Form Validation Example')),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            CustomTextField(
              controller: _emailController,
              labelText: 'Email',
              validationRules: ValidationRules.requiredEmail(),
              onValidationChanged: (result) => _updateFormValidation('email', result.isValid),
            ),
            const YBox(16),
            CustomTextField(
              controller: _passwordController,
              labelText: 'Password',
              isPassword: true,
              validationRules: ValidationRules.password(),
              onValidationChanged: (result) => _updateFormValidation('password', result.isValid),
            ),
            const YBox(16),
            CustomTextField(
              controller: _confirmPasswordController,
              labelText: 'Confirm Password',
              isPassword: true,
              validationRules: [
                RequiredRule(fieldName: 'password confirmation'),
                CustomRule(
                  validator: (value) {
                    if (value != _passwordController.text) {
                      return 'Passwords do not match';
                    }
                    return null;
                  },
                ),
              ],
              onValidationChanged: (result) => _updateFormValidation('confirmPassword', result.isValid),
            ),
            const YBox(32),
            CustomBtn.solid(
              onTap: _isFormValid ? _submitForm : null,
              text: 'Submit Form',
              online: _isFormValid,
            ),
          ],
        ),
      ),
    );
  }

  void _updateFormValidation(String fieldName, bool isValid) {
    setState(() {
      _fieldValidationStates[fieldName] = isValid;
      _isFormValid = _fieldValidationStates.values.every((valid) => valid) &&
                     _fieldValidationStates.length == 3; // All 3 fields validated
    });
  }

  void _submitForm() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Form submitted successfully!')),
    );
  }
}
