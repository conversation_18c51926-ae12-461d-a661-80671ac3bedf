import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:equalcash/ui/components/shared/textfields/validation/form_validator.dart';
import 'package:equalcash/ui/components/shared/textfields/validation/form_validator_widget.dart';

/// Example demonstrating different ways to use the new form validation system
class FormValidationExamplesScreen extends StatefulWidget {
  const FormValidationExamplesScreen({super.key});

  @override
  State<FormValidationExamplesScreen> createState() =>
      _FormValidationExamplesScreenState();
}

class _FormValidationExamplesScreenState
    extends State<FormValidationExamplesScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Form Validation Examples')),
      body: ListView(
        padding: EdgeInsets.all(16.w),
        children: [
          _buildExampleCard(
            'Method 1: FormValidator Utility',
            'Direct use of FormValidator class',
            () => Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const FormValidatorExample()),
            ),
          ),
          const YBox(16),
          _buildExampleCard(
            'Method 2: FormValidationMixin',
            'Using mixin for easy integration',
            () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) => const FormValidationMixinExample()),
            ),
          ),
          const YBox(16),
          _buildExampleCard(
            'Method 3: FormValidatorWidget',
            'Widget wrapper with auto-validation',
            () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) => const FormValidatorWidgetExample()),
            ),
          ),
          const YBox(16),
          _buildExampleCard(
            'Method 4: Context Extensions',
            'Using context extensions for convenience',
            () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) => const ContextExtensionExample()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExampleCard(
      String title, String description, VoidCallback onTap) {
    return Card(
      child: ListTile(
        title: Text(title,
            style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold)),
        subtitle: Text(description, style: AppTypography.text14),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }
}

/// Example 1: Using FormValidator utility class directly
class FormValidatorExample extends StatefulWidget {
  const FormValidatorExample({super.key});

  @override
  State<FormValidatorExample> createState() => _FormValidatorExampleState();
}

class _FormValidatorExampleState extends State<FormValidatorExample> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('FormValidator Utility')),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Text(
              'This example uses FormValidator.validateAllFields() directly',
              style: AppTypography.text14.copyWith(color: AppColors.neutral300),
            ),
            const YBox(24),
            CustomTextField(
              controller: _nameController,
              labelText: 'Full Name',
              validationRules: ValidationRules.requiredWithLength(
                fieldName: 'name',
                minLength: 2,
                maxLength: 50,
              ),
            ),
            const YBox(16),
            CustomTextField(
              controller: _emailController,
              labelText: 'Email',
              validationRules: ValidationRules.requiredEmail(),
            ),
            const YBox(16),
            CustomTextField(
              controller: _passwordController,
              labelText: 'Password',
              isPassword: true,
              validationRules: ValidationRules.password(),
            ),
            const YBox(32),
            Row(
              children: [
                Expanded(
                  child: CustomBtn.solid(
                    text: 'Validate Form',
                    onTap: () async {
                      final isValid =
                          await FormValidator.validateAllFields(context);
                      _showResult(isValid);
                    },
                  ),
                ),
                const XBox(16),
                Expanded(
                  child: CustomBtn.solid(
                    text: 'Clear Validation',
                    isOutline: true,
                    onTap: () => FormValidator.clearAllValidation(context),
                  ),
                ),
              ],
            ),
            const YBox(16),
            CustomBtn.solid(
              text: 'Check Current State',
              isOutline: true,
              onTap: () {
                final isValid = FormValidator.areAllFieldsValid(context);
                _showResult(isValid, isCurrentState: true);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showResult(bool isValid, {bool isCurrentState = false}) {
    final message = isCurrentState
        ? (isValid
            ? 'All fields are currently valid'
            : 'Some fields are currently invalid')
        : (isValid ? 'Form validation passed!' : 'Form validation failed!');

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isValid ? Colors.green : Colors.red,
      ),
    );
  }
}

/// Example 2: Using FormValidationMixin
class FormValidationMixinExample extends StatefulWidget {
  const FormValidationMixinExample({super.key});

  @override
  State<FormValidationMixinExample> createState() =>
      _FormValidationMixinExampleState();
}

class _FormValidationMixinExampleState extends State<FormValidationMixinExample>
    with FormValidationMixin {
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('FormValidationMixin')),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Text(
              'This example uses FormValidationMixin for convenient access',
              style: AppTypography.text14.copyWith(color: AppColors.neutral300),
            ),
            const YBox(24),
            CustomTextField(
              controller: _emailController,
              labelText: 'Email',
              validationRules: ValidationRules.requiredEmail(),
            ),
            const YBox(16),
            CustomTextField(
              controller: _phoneController,
              labelText: 'Phone Number',
              validationRules: ValidationRules.requiredWithLength(
                fieldName: 'phone number',
                minLength: 11,
                maxLength: 15,
                requiredMessage: 'Please enter your phone number',
                minLengthMessage: 'Phone number must be at least 11 characters',
              ),
            ),
            const YBox(32),
            Row(
              children: [
                Expanded(
                  child: CustomBtn.solid(
                    text: 'Submit Form',
                    onTap: () async {
                      // Using mixin methods directly
                      if (await validateForm()) {
                        _showSuccess();
                      } else {
                        _showError();
                      }
                    },
                  ),
                ),
                const XBox(16),
                Expanded(
                  child: CustomBtn.solid(
                    text: 'Clear Form',
                    isOutline: true,
                    onTap: () => clearFormValidation(), // Using mixin method
                  ),
                ),
              ],
            ),
            const YBox(16),
            Text(
              'Current form state: ${isFormCurrentlyValid ? "Valid" : "Invalid"}',
              style: AppTypography.text14.copyWith(
                color: isFormCurrentlyValid ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Form submitted successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showError() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Please fix the errors and try again'),
        backgroundColor: Colors.red,
      ),
    );
  }
}

/// Example 3: Using FormValidatorWidget wrapper
class FormValidatorWidgetExample extends StatefulWidget {
  const FormValidatorWidgetExample({super.key});

  @override
  State<FormValidatorWidgetExample> createState() =>
      _FormValidatorWidgetExampleState();
}

class _FormValidatorWidgetExampleState
    extends State<FormValidatorWidgetExample> {
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isFormValid = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('FormValidatorWidget')),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: FormValidatorWidget(
          autoValidate: true,
          onValidationChanged: (isValid) {
            setState(() {
              _isFormValid = isValid;
            });
          },
          child: Column(
            children: [
              Text(
                'This example uses FormValidatorWidget with auto-validation',
                style:
                    AppTypography.text14.copyWith(color: AppColors.neutral300),
              ),
              const YBox(24),
              CustomTextField(
                controller: _usernameController,
                labelText: 'Username',
                validationRules: ValidationRules.requiredWithLength(
                  fieldName: 'username',
                  minLength: 3,
                  maxLength: 20,
                ),
              ),
              const YBox(16),
              CustomTextField(
                controller: _passwordController,
                labelText: 'Password',
                isPassword: true,
                validationRules: ValidationRules.password(),
              ),
              const YBox(16),
              CustomTextField(
                controller: _confirmPasswordController,
                labelText: 'Confirm Password',
                isPassword: true,
                validationRules: [
                  const RequiredRule(fieldName: 'password confirmation'),
                  CustomRule(
                    validator: (value) {
                      if (value != _passwordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                  ),
                ],
              ),
              const YBox(32),
              CustomBtn.solid(
                text: 'Create Account',
                online: _isFormValid,
                onTap: _isFormValid ? _createAccount : null,
              ),
              const YBox(16),
              Text(
                'Form is ${_isFormValid ? "valid" : "invalid"} (auto-updated)',
                style: AppTypography.text14.copyWith(
                  color: _isFormValid ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _createAccount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Account created successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

/// Example 4: Using context extensions
class ContextExtensionExample extends StatefulWidget {
  const ContextExtensionExample({super.key});

  @override
  State<ContextExtensionExample> createState() =>
      _ContextExtensionExampleState();
}

class _ContextExtensionExampleState extends State<ContextExtensionExample> {
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Context Extensions')),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: FormValidatorWidget(
          child: Column(
            children: [
              Text(
                'This example uses context extensions for convenient access',
                style:
                    AppTypography.text14.copyWith(color: AppColors.neutral300),
              ),
              const YBox(24),
              CustomTextField(
                controller: _firstNameController,
                labelText: 'First Name',
                validationRules: ValidationRules.requiredWithLength(
                  fieldName: 'first name',
                  minLength: 2,
                  maxLength: 30,
                ),
              ),
              const YBox(16),
              CustomTextField(
                controller: _lastNameController,
                labelText: 'Last Name',
                validationRules: ValidationRules.requiredWithLength(
                  fieldName: 'last name',
                  minLength: 2,
                  maxLength: 30,
                ),
              ),
              const YBox(16),
              CustomTextField(
                controller: _emailController,
                labelText: 'Email',
                validationRules: ValidationRules.requiredEmail(),
              ),
              const YBox(32),
              Row(
                children: [
                  Expanded(
                    child: CustomBtn.solid(
                      text: 'Submit',
                      onTap: () async {
                        // Using context extension
                        if (await context.validateForm()) {
                          _showSuccess();
                        }
                      },
                    ),
                  ),
                  const XBox(16),
                  Expanded(
                    child: CustomBtn.solid(
                      text: 'Clear',
                      isOutline: true,
                      onTap: () => context
                          .clearFormValidation(), // Using context extension
                    ),
                  ),
                ],
              ),
              const YBox(16),
              Builder(
                builder: (context) => Text(
                  'Current state: ${context.isFormCurrentlyValid ? "Valid" : "Invalid"}',
                  style: AppTypography.text14.copyWith(
                    color: context.isFormCurrentlyValid
                        ? Colors.green
                        : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Form submitted successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
