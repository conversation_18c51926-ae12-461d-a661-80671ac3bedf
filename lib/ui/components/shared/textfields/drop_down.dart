import 'package:equalcash/core/core.dart';

class DropdownField extends StatefulWidget {
  final String label;
  final List<String> options;
  final String? hintText;
  final Function(String)? onOptionSelected;
  final bool isRequired;
  final String? initialValue;

  const DropdownField({
    super.key,
    required this.label,
    required this.options,
    this.hintText,
    this.onOptionSelected,
    this.isRequired = false,
    this.initialValue,
  });

  @override
  State<DropdownField> createState() => _DropdownFieldState();
}

class _DropdownFieldState extends State<DropdownField> {
  String? _selectedOption;
  bool _hasError = false;
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _selectedOption = widget.initialValue;
    if (_selectedOption != null) {
      _controller.text = _selectedOption!;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _showDropdown(BuildContext context) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset.zero, ancestor: overlay),
        button.localToGlobal(button.size.bottomRight(Offset.zero),
            ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );

    final String? selected = await showMenu<String>(
      context: context,
      position: position,
      items: widget.options.map((String option) {
        return PopupMenuItem<String>(
          value: option,
          child: Text(option),
        );
      }).toList(),
    );

    if (selected != null) {
      setState(() {
        _selectedOption = selected;
        _controller.text = selected;
        _hasError = false;
      });

      if (widget.onOptionSelected != null) {
        widget.onOptionSelected!(_selectedOption!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _showDropdown(context),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: _hasError ? Colors.red : Colors.grey.shade300,
              ),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _controller,
                    focusNode: _focusNode,
                    readOnly: true,
                    onTap: () => _showDropdown(context),
                    decoration: InputDecoration(
                      suffixIcon: Icon(
                        Iconsax.arrow_down_1,
                        color: Colors.grey.shade600,
                      ),
                      border: InputBorder.none,
                      hintText: widget.hintText,
                      errorText: _hasError ? "This field is required" : null,
                    ),
                    validator: (value) {
                      if (widget.isRequired && value!.isEmpty) {
                        setState(() {
                          _hasError = true;
                        });
                      } else {
                        setState(() {
                          _hasError = false;
                        });
                      }
                      return null;
                    },
                  ),
                ),
                // IconButton(
                //   icon:
                //       Icon(Icons.arrow_drop_down, color: Colors.grey.shade600),
                //   onPressed: () => _showDropdown(context),
                // ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
