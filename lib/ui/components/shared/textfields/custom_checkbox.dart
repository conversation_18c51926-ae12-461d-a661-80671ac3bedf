import 'package:equalcash/core/core.dart';

class CustomCheckbox extends StatelessWidget {
  const CustomCheckbox({
    super.key,
    this.onTap,
    this.isSelected = false,
    this.borderRadius,
  });

  final VoidCallback? onTap;
  final bool isSelected;
  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        // padding: EdgeInsets.all(Sizer.radius(4)),
        width: Sizer.width(24),
        height: Sizer.height(24),
        decoration: BoxDecoration(
          // color: AppColors.white,
          borderRadius: BorderRadius.circular(Sizer.radius(borderRadius ?? 6)),
          border: Border.all(
            color: isSelected ? AppColors.transparent : AppColors.primaryPurple,
            width: Sizer.width(1.5),
          ),
        ),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primaryPurple : AppColors.transparent,
            borderRadius: BorderRadius.circular(Sizer.radius(6)),
          ),
          width: isSelected ? Sizer.width(20) : 0,
          height: isSelected ? Sizer.height(20) : 0,
          child: isSelected
              ? Center(
                  child: Icon(
                  Icons.check,
                  color: AppColors.white,
                  size: Sizer.text(16),
                ))
              : null,
        ),
      ),
    );
  }
}
