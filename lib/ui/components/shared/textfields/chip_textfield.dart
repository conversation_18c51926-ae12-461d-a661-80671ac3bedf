import 'package:flutter/material.dart';

class InputChipTextBox extends StatelessWidget {
  final TextEditingController controller;
  final List<String> chips;
  final Function(String) onDeleteChip;
  final Function(String) onSubmitted;
  final Function(String) onChanged;
  final bool isValidInput;
  final String hintText;

  const InputChipTextBox({
    super.key,
    required this.controller,
    required this.chips,
    required this.onDeleteChip,
    required this.onSubmitted,
    required this.onChanged,
    required this.isValidInput,
    required this.hintText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade100,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          ...chips.map(
            (chip) => InputChip(
              backgroundColor: Colors.grey.shade200,
              deleteIconColor: Colors.grey,
              label: Text(chip),
              onDeleted: () => onDeleteChip(chip),
              deleteIcon: const Icon(Icons.cancel, size: 18),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            ),
          ),
          IntrinsicWidth(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: chips.isEmpty ? hintText : '',
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 8),
                isDense: true,
              ),
              style: TextStyle(
                color: isValidInput ? Colors.black : Colors.red,
              ),
              onSubmitted: onSubmitted,
              onChanged: onChanged,
              textInputAction: TextInputAction.done,
            ),
          ),
        ],
      ),
    );
  }
}
