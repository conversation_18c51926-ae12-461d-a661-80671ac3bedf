import 'package:flutter/material.dart';

class ChipTextField3 extends FormField<List<String>> {
  ChipTextField3({
    super.key,
    required this.chipMatchColor,
    required this.fromMatch,
    this.hintText,
    this.borderRadius = 12.0,
    this.backgroundColor = const Color(0xFFF3F3F3),
    super.validator,
  }) : super(
          initialValue: const [],
          builder: (FormFieldState<List<String>> state) {
            return _ChipTextFieldBody(
              state: state,
              chipMatchColor: chipMatchColor,
              fromMatch: fromMatch,
              hintText: hintText,
              backgroundColor: backgroundColor,
              borderRadius: borderRadius,
            );
          },
        );

  final Color chipMatchColor;
  final Function(List<String> matched) fromMatch;
  final String? hintText;
  final Color backgroundColor;
  final double borderRadius;
}

class _ChipTextFieldBody extends StatefulWidget {
  final FormFieldState<List<String>> state;
  final Color chipMatchColor;
  final Function(List<String> matched) fromMatch;
  final String? hintText;
  final Color backgroundColor;
  final double borderRadius;

  const _ChipTextFieldBody({
    required this.state,
    required this.chipMatchColor,
    required this.fromMatch,
    required this.backgroundColor,
    required this.borderRadius,
    this.hintText,
  });

  @override
  State<_ChipTextFieldBody> createState() => _ChipTextFieldBodyState();
}

class _ChipTextFieldBodyState extends State<_ChipTextFieldBody> {
  final TextEditingController _controller = TextEditingController();
  final List<String> _matches = [];

  final _emailRegex = RegExp(r"^[\w\.\-]+@[\w\-]+\.[\w\-\.]+$");
  final _phoneRegex = RegExp(r"^\+?\d{7,15}$");

  void _parseAndAdd(String input) {
    final entries = input
        .split(RegExp(r"[,\s]+"))
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty);

    final validEntries = entries
        .where((e) => _emailRegex.hasMatch(e) || _phoneRegex.hasMatch(e));

    setState(() {
      for (var e in validEntries) {
        if (!_matches.contains(e)) _matches.add(e);
      }
      _controller.clear();
      widget.state.didChange(_matches);
      widget.fromMatch(_matches);
    });
  }

  String _displayLabel(String match) {
    if (_emailRegex.hasMatch(match)) {
      final local = match.split("@")[0];
      return local.length > 10 ? '${local.substring(0, 10)}...' : local;
    }
    return match.length > 10 ? '${match.substring(0, 10)}...' : match;
  }

  void _editChip(String current) {
    setState(() {
      _matches.remove(current);
      _controller.text = current;
      _controller.selection = TextSelection.collapsed(offset: current.length);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Wrap(
        spacing: 6,
        runSpacing: 6,
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          ..._matches.map(
            (e) => InputChip(
              label: Text(
                _displayLabel(e),
                style: const TextStyle(fontSize: 14),
              ),
              onPressed: () => _editChip(e),
              onDeleted: () {
                setState(() {
                  _matches.remove(e);
                  widget.state.didChange(_matches);
                  widget.fromMatch(_matches);
                });
              },
              backgroundColor: Colors.grey.shade200,
              deleteIcon: const Icon(Icons.close, size: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          ConstrainedBox(
            constraints: const BoxConstraints(minWidth: 80, maxWidth: 150),
            child: TextFormField(
              controller: _controller,
              decoration: InputDecoration(
                hintText: widget.hintText ?? "Type here...",
                border: InputBorder.none,
                isDense: true,
                contentPadding: const EdgeInsets.symmetric(vertical: 8),
              ),
              onChanged: (value) {
                if (value.endsWith(" ") || value.endsWith(",")) {
                  _parseAndAdd(value);
                }
              },
              onFieldSubmitted: (value) {
                if (value.isNotEmpty) {
                  _parseAndAdd(value);
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
