import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import 'package:intl/intl.dart';

class DatePickerField extends StatefulWidget {
  final String label;
  final String? hintText;
  final Function(DateTime)? onDateSelected;
  final bool isRequired;
  final DateTime? initialDate;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const DatePickerField({
    super.key,
    required this.label,
    this.hintText,
    this.onDateSelected,
    this.isRequired = false,
    this.initialDate,
    this.firstDate,
    this.lastDate,
  });

  @override
  State<DatePickerField> createState() => _DatePickerFieldState();
}

class _DatePickerFieldState extends State<DatePickerField> {
  DateTime? _selectedDate;
  bool _hasError = false;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    if (_selectedDate != null) {
      _updateControllerText();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _updateControllerText() {
    if (_selectedDate != null) {
      _controller.text = DateFormat('dd/MM/yy').format(_selectedDate!);
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime initialDate = _selectedDate ?? now;
    final DateTime firstDate = widget.firstDate ?? now;
    final DateTime lastDate =
        widget.lastDate ?? DateTime(now.year + 10, now.month, now.day);

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.purple, // Header background color
              onPrimary: Colors.white, // Header text color
              onSurface: Colors.black, // Calendar text color
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.purple, // Button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _hasError = false;
        _updateControllerText();
      });

      if (widget.onDateSelected != null) {
        widget.onDateSelected!(_selectedDate!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _selectDate(context),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: _hasError ? Colors.red : Colors.grey.shade300,
              ),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _controller,
                    enabled: false,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: widget.hintText ?? "DD/MM/YY",
                      hintStyle: TextStyle(
                        color: Colors.grey.shade400,
                      ),
                      errorText: _hasError ? "Date is required" : null,
                    ),
                    validator: (value) {
                      if (widget.isRequired && value!.isEmpty) {
                        setState(() {
                          _hasError = true;
                        });
                      } else {
                        setState(() {
                          _hasError = false;
                        });
                      }
                      return null;
                    },
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    Iconsax.calendar_1,
                  ),
                  onPressed: () => _selectDate(context),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
