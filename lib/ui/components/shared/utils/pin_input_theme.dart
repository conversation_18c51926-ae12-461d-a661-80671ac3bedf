import 'package:equalcash/core/core.dart';
import 'package:pinput/pinput.dart';

class PinInputTheme {
  static defaultPinTheme({double? borderRadius, Color? bgColor}) {
    return PinTheme(
      width: Sizer.width(48),
      height: Sizer.height(48),
      // margin: EdgeInsets.only(right: 8.w),
      textStyle: TextStyle(
        fontSize: Sizer.text(20),
        color: AppColors.black,
        fontWeight: FontWeight.w500,
      ),
      decoration: BoxDecoration(
        color: bgColor ?? AppColors.white,
        border: Border.all(
          color: AppColors.grayFE,
          width: 0,
        ),
        borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
      ),
    );
  }

  static followPinTheme({double? borderRadius}) {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(
        width: Sizer.width(1),
        color: AppColors.secondary300,
      ),
      borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
      // color: AppColors.grayF7,
    );
  }

  static fillDefaultPinTheme() {
    return PinTheme(
      width: Sizer.width(48),
      height: Sizer.height(48),
      // margin: EdgeInsets.only(right: 8.w),
      textStyle: TextStyle(
        fontSize: Sizer.text(20),
        color: AppColors.black,
        fontWeight: FontWeight.w500,
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(
          color: AppColors.grayFE,
          width: 0,
        ),
      ),
    );
  }

  static focusFillPinTheme() {
    return defaultPinTheme().copyDecorationWith(
        border: Border.all(width: 0, color: AppColors.grayFE),
        color: AppColors.primaryPurple.withOpacity(0.1));
  }

  static changePinTheme() {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: 0, color: AppColors.primaryPurple),
      // color: AppColors.primaryOrange,
    );
  }
}
