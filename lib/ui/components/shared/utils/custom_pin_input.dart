import 'package:equalcash/ui/components/components.dart';
import 'package:flutter/services.dart';

class CustomPinInput extends StatefulWidget {
  final Function(String) onCompleted;
  final String label;
  final int pinLength;
  final bool obscureText;
  final TextStyle? textStyle;
  final TextStyle? labelStyle;
  final Color? borderColor;
  final Color? activeBorderColor;

  const CustomPinInput({
    super.key,
    required this.onCompleted,
    this.label = 'Use your 4 digit pin to login faster',
    this.pinLength = 4,
    this.obscureText = false,
    this.textStyle,
    this.labelStyle,
    this.borderColor,
    this.activeBorderColor,
  });

  @override
  State<CustomPinInput> createState() => _CustomPinInputState();
}

class _CustomPinInputState extends State<CustomPinInput> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  late List<bool> _hasFocus;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.pinLength,
      (index) => TextEditingController(),
    );
    _focusNodes = List.generate(
      widget.pinLength,
      (index) => FocusNode(),
    );
    _hasFocus = List.generate(widget.pinLength, (index) => false);

    // Add listeners to focus nodes
    for (int i = 0; i < widget.pinLength; i++) {
      _focusNodes[i].addListener(() {
        setState(() {
          _hasFocus[i] = _focusNodes[i].hasFocus;
        });
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  void _onChanged(String value, int index) {
    if (value.isNotEmpty) {
      // If user pasted a code, handle it
      if (value.length > 1) {
        _handlePaste(value, index);
        return;
      }

      // Move to next field
      if (index < widget.pinLength - 1) {
        _focusNodes[index + 1].requestFocus();
      } else {
        // Last field, check if PIN is complete
        _focusNodes[index].unfocus();
        _checkPinComplete();
      }
    }
  }

  void _handlePaste(String value, int currentIndex) {
    // Only take as many characters as we have fields remaining
    final endIndex = (currentIndex + value.length).clamp(0, widget.pinLength);
    final effectiveLength = endIndex - currentIndex;
    final pasteValue = value.substring(0, effectiveLength);

    // Fill the fields with the pasted value
    for (int i = 0; i < effectiveLength; i++) {
      final charIndex = currentIndex + i;
      _controllers[charIndex].text = pasteValue[i];
    }

    // Move focus to the last filled field or submit if complete
    if (endIndex < widget.pinLength) {
      _focusNodes[endIndex].requestFocus();
    } else {
      _focusNodes[widget.pinLength - 1].unfocus();
      _checkPinComplete();
    }
  }

  void _onKey(RawKeyEvent event, int index) {
    if (event is RawKeyDownEvent &&
        event.logicalKey == LogicalKeyboardKey.backspace) {
      if (_controllers[index].text.isEmpty && index > 0) {
        // Move to previous field and clear it
        _focusNodes[index - 1].requestFocus();
        _controllers[index - 1].text = '';
      }
    }
  }

  void _checkPinComplete() {
    final pin = _controllers.map((c) => c.text).join();
    if (pin.length == widget.pinLength) {
      widget.onCompleted(pin);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              widget.label,
              style: widget.labelStyle ??
                  const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
            ),
          ),
        Container(
          height: 52,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: _hasFocus.contains(true)
                  ? widget.activeBorderColor ?? Colors.blue
                  : widget.borderColor ?? Colors.grey,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: List.generate(
              widget.pinLength,
              (index) => Expanded(
                child: RawKeyboardListener(
                  focusNode: FocusNode(), // Needed to capture key events
                  onKey: (event) => _onKey(event, index),
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      border: index < widget.pinLength - 1
                          ? Border(
                              right: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            )
                          : null,
                    ),
                    child: TextField(
                      controller: _controllers[index],
                      focusNode: _focusNodes[index],
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      maxLength: 1,
                      obscureText: widget.obscureText,
                      obscuringCharacter: '•',
                      style: widget.textStyle ??
                          const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                      decoration: const InputDecoration(
                        counterText: '',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      onChanged: (value) => _onChanged(value, index),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// class CustomPinInput extends StatefulWidget {
//   final Function(String) onCompleted;
//   final String label;

//   const CustomPinInput({
//     super.key,
//     required this.onCompleted,
//     this.label = 'Use your 4 digit pin to login faster',
//   });

//   @override
//   State<CustomPinInput> createState() => _CustomPinInputState();
// }

// class _CustomPinInputState extends State<CustomPinInput> {
//   final List<TextEditingController> _controllers = List.generate(
//     4,
//     (index) => TextEditingController(),
//   );
//   final List<FocusNode> _focusNodes = List.generate(
//     4,
//     (index) => FocusNode(),
//   );

//   @override
//   void dispose() {
//     for (var controller in _controllers) {
//       controller.dispose();
//     }
//     for (var node in _focusNodes) {
//       node.dispose();
//     }
//     super.dispose();
//   }

//   void _onChanged(String value, int index) {
//     if (value.length == 1) {
//       // Move to next field
//       if (index < 3) {
//         _focusNodes[index + 1].requestFocus();
//       } else {
//         // Last field, check if PIN is complete
//         _focusNodes[index].unfocus();
//         final pin = _controllers.map((c) => c.text).join();
//         if (pin.length == 4) {
//           widget.onCompleted(pin);
//         }
//       }
//     } else if (value.isEmpty && index > 0) {
//       // Backspace pressed, move to previous field
//       _focusNodes[index - 1].requestFocus();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Padding(
//           padding: EdgeInsets.only(bottom: Sizer.height(8)),
//           child: Text(
//             widget.label,
//             style: const TextStyle(
//               fontSize: 16,
//               color: Colors.black87,
//             ),
//           ),
//         ),
//         Container(
//           height: Sizer.height(52),
//           decoration: BoxDecoration(
//             color: AppColors.white,
//             border: Border.all(color: AppColors.secondary300),
//             borderRadius: BorderRadius.circular(8),
//           ),
//           child: Row(
//             children: List.generate(
//               4,
//               (index) => Expanded(
//                 child: Container(
//                   margin: EdgeInsets.symmetric(
//                     vertical: Sizer.height(10),
//                   ),
//                   decoration: BoxDecoration(
//                     border: index < 3
//                         ? Border(
//                             right: BorderSide(
//                               color: Colors.grey.shade300,
//                             ),
//                           )
//                         : null,
//                   ),
//                   child: TextField(
//                     controller: _controllers[index],
//                     focusNode: _focusNodes[index],
//                     textAlign: TextAlign.center,
//                     keyboardType: TextInputType.number,
//                     maxLength: 1,
//                     style: const TextStyle(fontSize: 20),
//                     decoration: const InputDecoration(
//                       counterText: '',
//                       border: InputBorder.none,
//                       contentPadding: EdgeInsets.symmetric(vertical: 12),
//                     ),
//                     inputFormatters: [
//                       FilteringTextInputFormatter.digitsOnly,
//                     ],
//                     onChanged: (value) => _onChanged(value, index),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
