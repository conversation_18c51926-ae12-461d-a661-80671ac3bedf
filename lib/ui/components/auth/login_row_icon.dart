import 'package:equalcash/core/core.dart';

class LoginRowIcon extends StatelessWidget {
  const LoginRowIcon({
    super.key,
    required this.svgPath,
    required this.text,
    this.onTap,
  });

  final String svgPath;
  final String text;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          svgHelper(
            svgPath,
            height: Sizer.height(32),
            width: Sizer.width(32),
          ),
          const XBox(10),
          Text(
            text,
            style: AppTypography.text16.copyWith(
              color: AppColors.white.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }
}
