import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class BankAccountScreen extends ConsumerStatefulWidget {
  const BankAccountScreen({super.key});

  @override
  ConsumerState<BankAccountScreen> createState() => _DebitCardScreenState();
}

class _DebitCardScreenState extends ConsumerState<BankAccountScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(bankVmodel).getMyBankAccounts();
    });
  }

  @override
  Widget build(BuildContext context) {
    final bankRef = ref.watch(bankVmodel);
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(
        headerText: 'Bank Account',
      ),
      body: LoadableContentBuilder(
        isError: bankRef.hasError,
        isBusy: bankRef.isBusy,
        items: bankRef.bankAccounts,
        loadingBuilder: (context) {
          return ListView.separated(
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              top: Sizer.height(24),
              bottom: Sizer.height(100),
            ),
            itemCount: 5,
            separatorBuilder: (_, __) => const YBox(16),
            itemBuilder: (ctx, i) {
              return Skeletonizer(
                enabled: true,
                child: BankAccountCard(
                  bankAccount: BankAccountModel(
                    accountNumber: '**********',
                    accountName: 'Adebayo Kolawole',
                    bankCode: '044',
                    bankName: 'Access Bank',
                  ),
                ),
              );
            },
          );
        },
        errorBuilder: (context) {
          return EmptyListState(
            text: 'Failed to load bank accounts',
            onRetry: () {
              ref.read(bankVmodel).getMyBankAccounts();
            },
          );
        },
        emptyBuilder: (context) {
          return Center(
            child: Text(
              "No bank accounts found",
              style: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.neutral300,
              ),
            ),
          );
        },
        contentBuilder: (context) {
          return ListView.separated(
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              top: Sizer.height(24),
              bottom: Sizer.height(100),
            ),
            itemCount: bankRef.bankAccounts.length,
            separatorBuilder: (_, __) => const YBox(16),
            itemBuilder: (ctx, i) {
              final bankAccount = bankRef.bankAccounts[i];
              return BankAccountCard(bankAccount: bankAccount);
            },
          );
        },
      ),
      bottomSheet: bankRef.isBusy || bankRef.bankAccounts.isNotEmpty
          ? null
          : Container(
              color: AppColors.purpleF1,
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
                bottom: Sizer.height(30),
              ),
              child: CustomBtn.solid(
                text: 'Add Bank Account',
                textStyle: AppTypography.text16.copyWith(
                  color: AppColors.white,
                ),
                onTap: () {
                  NavigationHelper.navigateTo(
                    routeName: RoutePath.addBankAccountScreen,
                  );
                },
              ),
            ),
    );
  }
}

class BankAccountCard extends StatelessWidget {
  const BankAccountCard({
    super.key,
    required this.bankAccount,
  });

  final BankAccountModel bankAccount;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Sizer.radius(16)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(
          Sizer.radius(12),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: BankAcctColumnText(
                  title: 'Bank',
                  value: bankAccount.bankName ?? '',
                ),
              ),
              const XBox(16),
              BankAcctColumnText(
                title: 'Account Number',
                value: bankAccount.accountNumber ?? '',
              ),
            ],
          ),
          const YBox(16),
          BankAcctColumnText(
            title: 'Account Holder',
            value: bankAccount.accountName ?? '',
          ),
        ],
      ),
    );
  }
}

class BankAcctColumnText extends StatelessWidget {
  const BankAcctColumnText({
    super.key,
    required this.title,
    required this.value,
    this.crossAxisAlignment,
  });

  final String title;
  final String value;
  final CrossAxisAlignment? crossAxisAlignment;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text12.copyWith(
            color: AppColors.neutral200,
          ),
        ),
        const YBox(8),
        Text(
          value,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTypography.text14.copyWith(
            fontWeight: FontWeight.w500,
            color: AppColors.neutral400,
          ),
        ),
      ],
    );
  }
}
