import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:flutter/services.dart';

class AddCardScreen extends StatefulWidget {
  const AddCardScreen({super.key});

  @override
  State<AddCardScreen> createState() => _AddCardScreenState();
}

class _AddCardScreenState extends State<AddCardScreen> {
  // Controllers for the text fields
  final TextEditingController _cardNumberController = TextEditingController();
  final TextEditingController _cardHolderController = TextEditingController();
  final TextEditingController _expiryDateController = TextEditingController();
  final TextEditingController _cvvController = TextEditingController();

  // Focus nodes for the text fields
  final FocusNode _cardNumberFocus = FocusNode();
  final FocusNode _cardHolderFocus = FocusNode();
  final FocusNode _expiryDateFocus = FocusNode();
  final FocusNode _cvvFocus = FocusNode();

  // Card data that will be updated as user types
  String _cardNumber = '****   ****   ****   ****   1234';
  String _cardHolder = 'YOUR NAME';
  String _expiryDate = 'MM/YY';
  String _cvv = '***';

  @override
  void initState() {
    super.initState();

    // Add listeners to update card data as user types
    _cardNumberController.addListener(_updateCardNumber);
    _cardHolderController.addListener(_updateCardHolder);
    _expiryDateController.addListener(_updateExpiryDate);
    _cvvController.addListener(_updateCvv);
  }

  @override
  void dispose() {
    // Remove listeners
    _cardNumberController.removeListener(_updateCardNumber);
    _cardHolderController.removeListener(_updateCardHolder);
    _expiryDateController.removeListener(_updateExpiryDate);
    _cvvController.removeListener(_updateCvv);

    // Dispose controllers
    _cardNumberController.dispose();
    _cardHolderController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();

    // Dispose focus nodes
    _cardNumberFocus.dispose();
    _cardHolderFocus.dispose();
    _expiryDateFocus.dispose();
    _cvvFocus.dispose();

    super.dispose();
  }

  // Format card number with spaces
  void _updateCardNumber() {
    final text = _cardNumberController.text;
    if (text.isEmpty) {
      setState(() {
        _cardNumber = '****   ****   ****   ****   1234';
      });
      return;
    }

    // Format card number with spaces after every 4 digits
    String formattedNumber = '';
    for (int i = 0; i < text.length; i++) {
      if (i > 0 && i % 4 == 0) {
        formattedNumber += '   ';
      }
      formattedNumber += text[i];
    }

    setState(() {
      _cardNumber = formattedNumber;
    });
  }

  // Update card holder name
  void _updateCardHolder() {
    final text = _cardHolderController.text;
    setState(() {
      _cardHolder = text.isEmpty ? 'YOUR NAME' : text.toUpperCase();
    });
  }

  // Format expiry date as MM/YY
  void _updateExpiryDate() {
    final text = _expiryDateController.text;
    if (text.isEmpty) {
      setState(() {
        _expiryDate = 'MM/YY';
      });
      return;
    }

    // Format as MM/YY
    if (text.length <= 2) {
      setState(() {
        _expiryDate = text;
      });
    } else {
      setState(() {
        _expiryDate = '${text.substring(0, 2)}/${text.substring(2)}';
      });
    }
  }

  void _updateCvv() {
    final text = _cvvController.text;
    setState(() {
      _cvv = text.isEmpty ? '***' : text;
    });
  }

  bool get btnIsActive =>
      _cardNumberController.text.length == 16 &&
      _cardHolderController.text.isNotEmpty &&
      _expiryDateController.text.length == 4 &&
      _cvvController.text.length == 3;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(
        headerText: 'Add Card',
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
        child: ListView(
          children: [
            const YBox(24),
            CreditCardView(
              cardNumber: _cardNumber,
              cardHolder: _cardHolder,
              expiryDate: _expiryDate,
              cvv: _cvv,
              backgroundImage: AppImages.card,
              cardTypeLogo: AppSvgs.visa,
            ),
            const YBox(16),
            CustomTextField(
              controller: _cardNumberController,
              focusNode: _cardNumberFocus,
              labelText: 'Card Number',
              showLabelHeader: true,
              keyboardType: KeyboardType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(16),
              ],
              prefixIcon: Icon(
                Iconsax.card5,
                size: Sizer.radius(24),
                color: AppColors.neutral200,
              ),
            ),
            const YBox(24),
            CustomTextField(
              controller: _cardHolderController,
              focusNode: _cardHolderFocus,
              labelText: 'Card Holder',
              showLabelHeader: true,
              onChanged: (value) {
                // Convert to uppercase as user types
                if (value != value.toUpperCase()) {
                  _cardHolderController.value = TextEditingValue(
                    text: value.toUpperCase(),
                    selection: _cardHolderController.selection,
                  );
                }
              },
            ),
            const YBox(24),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _expiryDateController,
                    focusNode: _expiryDateFocus,
                    labelText: 'Expiry Date',
                    showLabelHeader: true,
                    keyboardType: KeyboardType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(4),
                    ],
                    hintText: 'MMYY',
                  ),
                ),
                const XBox(10),
                Expanded(
                  child: CustomTextField(
                    controller: _cvvController,
                    focusNode: _cvvFocus,
                    labelText: 'CVV',
                    showLabelHeader: true,
                    keyboardType: KeyboardType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(3),
                    ],
                    isPassword: true,
                  ),
                ),
              ],
            ),
            const YBox(40),
            CustomBtn.solid(
              text: 'Add Card',
              textStyle: AppTypography.text16.copyWith(
                color: AppColors.white,
              ),
              online: btnIsActive,
              onTap: () {},
            ),
            const YBox(16),
            SkipForNow(
              onTap: () {
                Navigator.pop(context);
              },
            ),
            const YBox(100),
          ],
        ),
      ),
    );
  }
}
