import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class InvitationDetailsScreen extends ConsumerStatefulWidget {
  const InvitationDetailsScreen({
    super.key,
    required this.url,
  });

  final String url;

  @override
  ConsumerState<InvitationDetailsScreen> createState() =>
      _InvitationDetailsScreenState();
}

class _InvitationDetailsScreenState
    extends ConsumerState<InvitationDetailsScreen> {
  CompanyModel? company;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      printty("url ${widget.url} ");
      _initSetup();
    });
  }

  _initSetup() async {
    final r = await ref.read(companyVmodel).getDynamicTypePath(widget.url);
    if (r.success) {
      company = CompanyModel.fromJson(r.data["data"]);
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final companyRef = ref.watch(companyVmodel);
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: LoadableContentBuilder(
          isBusy: companyRef.isBusy,
          isError: company == null,
          loadingBuilder: (context) {
            return const SizerLoader(height: 600);
          },
          errorBuilder: (context) {
            return EmptyListState(
              text: 'Failed to load company details',
              onRetry: () {
                _initSetup();
              },
            );
          },
          contentBuilder: (context) {
            return ListView(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              children: [
                const YBox(20),
                const CustomSubHeader(
                  title: 'Invitation',
                  subtitle:
                      "You are invited to join a contributing \ncompany created by Ademola Benson",
                ),
                const YBox(30),
                ColumnText(
                  title: 'Company’s Name',
                  subtitle: company?.name ?? '',
                ),
                const YBox(18),
                ColumnText(
                  title: 'Cash Bucket (Targeted amount)',
                  subtitle: "${AppConst.naira}${AppUtils.formatNumber(
                    number: double.tryParse(company?.cashBucket ?? '0') ?? 0,
                    decimalPlaces: 2,
                  )}",
                ),
                const YBox(18),
                ColumnText(
                  title: 'Cash Drop (Monthly contribution per person)',
                  subtitle: "${AppConst.naira}${AppUtils.formatNumber(
                    number: double.tryParse(company?.cashDrop ?? '0') ?? 0,
                    decimalPlaces: 2,
                  )}",
                ),
                const YBox(18),
                ColumnText(
                  title: 'Duration',
                  subtitle: '${company?.duration ?? 0} months',
                ),
                const YBox(18),
                ColumnText(
                  title: 'Commencement Date',
                  subtitle: AppUtils.daymy(
                      company?.commencementDate ?? DateTime.now()),
                ),
                const YBox(18),
                ColumnText(
                  title: 'CNumber of Stakeholders',
                  subtitle: '${company?.numberOfStakeholders ?? 0} members',
                ),
                const YBox(18),
                ColumnText(
                  title: 'Cash Reserve Ratio (10% of total collection)',
                  subtitle: "${AppConst.naira}${AppUtils.formatNumber(
                    number:
                        double.tryParse(company?.cashReserveRatio ?? '0') ?? 0,
                    decimalPlaces: 2,
                  )}",
                ),
                const YBox(18),
                ColumnText(
                  title: 'Collection Order Exchange Fee ',
                  subtitle: "${AppConst.naira}${AppUtils.formatNumber(
                    number:
                        double.tryParse(company?.cashReserveRatio ?? '0') ?? 0,
                    decimalPlaces: 2,
                  )}",
                ),
                const YBox(40),
                Row(
                  children: [
                    Expanded(
                      child: CustomBtn.solid(
                        text: 'Accept',
                        textStyle: AppTypography.text16.copyWith(
                          color: AppColors.white,
                        ),
                        onTap: () {
                          final loadingProvider =
                              StateProvider<bool>((ref) => false);

                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: Consumer(builder: (ctx, ref, _) {
                              final isLoading = ref.watch(loadingProvider);
                              return InfoActionModal(
                                title: 'Accept',
                                content:
                                    'Accepting shows that you agree to \nrules of the comapny',
                                btnText: 'Accept',
                                textBtnText: 'Cancel',
                                isLoading: isLoading,
                                onTapTextBtn: () {
                                  Navigator.pop(context);
                                },
                                onTapSoldBtn: () {
                                  ref.read(loadingProvider.notifier).state =
                                      true;
                                  ref
                                      .read(companyVmodel)
                                      .acceptOrRejectCompanyInvitation(
                                        companyId: company?.id ?? 0,
                                        acceptInvitation: true,
                                      )
                                      .then((value) {
                                    ref.read(loadingProvider.notifier).state =
                                        false;
                                    handleApiResponse(
                                      response: value,
                                      onCompleted: () {
                                        Navigator.pop(context);
                                      },
                                    );
                                  });
                                },
                              );
                            }),
                          );
                        },
                      ),
                    ),
                    const XBox(6),
                    Expanded(
                      child: CustomBtn.solid(
                        text: 'Decline',
                        textStyle: AppTypography.text16.copyWith(
                          color: AppColors.white,
                        ),
                        onlineColor:
                            AppColors.primaryPurple.withValues(alpha: 0.7),
                        onTap: () {
                          final loadingProvider =
                              StateProvider<bool>((ref) => false);
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: Consumer(builder: (ctx, ref, _) {
                              final isLoading = ref.watch(loadingProvider);
                              return InfoActionModal(
                                title: 'Decline',
                                content:
                                    'Declining will take you out of the Unique brothers company',
                                btnText: 'Cancel',
                                isLoading: isLoading,
                                textBtnText: 'Yes, Take me out',
                                onTapTextBtn: () {
                                  ref.read(loadingProvider.notifier).state =
                                      true;
                                  ref
                                      .read(companyVmodel)
                                      .acceptOrRejectCompanyInvitation(
                                        companyId: company?.id ?? 0,
                                        acceptInvitation: false,
                                      )
                                      .then((value) {
                                    ref.read(loadingProvider.notifier).state =
                                        false;
                                    handleApiResponse(
                                      response: value,
                                      onCompleted: () {
                                        Navigator.pop(context);
                                        ModalWrapper.bottomSheet(
                                          context: context,
                                          widget: InfoActionModal(
                                            title:
                                                'Removed from Unique Brothers',
                                            content:
                                                'You have been removed from the Unique brothers company',
                                            btnText: 'Proceed To Dashboard',
                                            onTapSoldBtn: () {
                                              Navigator.pushNamedAndRemoveUntil(
                                                NavKey.appNavigatorKey
                                                    .currentContext!,
                                                RoutePath
                                                    .dashboardNavigationScreen,
                                                (r) => false,
                                              );
                                            },
                                          ),
                                        );
                                      },
                                    );
                                  });
                                },
                                onTapSoldBtn: () {},
                              );
                            }),
                          );
                        },
                      ),
                    ),
                    const XBox(6),
                    Expanded(
                      child: CustomBtn.solid(
                        text: 'Conditional',
                        textStyle: AppTypography.text16.copyWith(
                          color: AppColors.white,
                        ),
                        onTap: () {
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: const ConditionalContributionModal(),
                          );
                        },
                      ),
                    ),
                  ],
                ),
                const YBox(100),
              ],
            );
          }),
    );
  }
}
