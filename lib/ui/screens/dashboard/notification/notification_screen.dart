import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class NotificationScreen extends ConsumerStatefulWidget {
  const NotificationScreen({super.key});

  @override
  ConsumerState<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends ConsumerState<NotificationScreen> {
  NotificationType selectedNotificationType = NotificationType.transactions;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationVm).getAllNotifications(true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final notificationRef = ref.watch(notificationVm);
        return Scaffold(
          appBar: CustomDashboardAppbar(
            showSearchField: false,
            height: 100,
            topWidget: Text(
              'Notifications',
              style: AppTypography.text24.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          body: Container(
            color: AppColors.purpleF1,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Column(
              children: [
                const YBox(16),
                Row(
                  children:
                      List.generate(NotificationType.values.length, (index) {
                    final notificationType = NotificationType.values[index];
                    return MiniBtn(
                      text: notificationType.value,
                      isSelected: notificationType == selectedNotificationType,
                      onTap: () {
                        setState(() {
                          selectedNotificationType = notificationType;
                        });
                      },
                    );
                  }),
                ),
                Expanded(
                  child: LoadableContentBuilder(
                      isBusy: notificationRef.isBusy,
                      items: notificationRef.allNotifications,
                      loadingBuilder: (context) {
                        return ListView.separated(
                          padding: EdgeInsets.only(
                            top: Sizer.height(16),
                            bottom: Sizer.height(100),
                          ),
                          itemBuilder: (ctx, i) {
                            return Skeletonizer(
                              enabled: true,
                              child: NotificationTile(
                                title: "Contribution Company Invitation",
                                subtitle:
                                    "You are invited by Adebayo Salami to join a monthly contribution scheme...",
                                createdAt: DateTime.now(),
                              ),
                            );
                          },
                          separatorBuilder: (_, __) => const YBox(16),
                          itemCount: 8,
                        );
                      },
                      emptyBuilder: (context) {
                        return const EmptyListState(
                          text: 'No notifications found',
                        );
                      },
                      contentBuilder: (context) {
                        return ListView(
                          padding: EdgeInsets.only(
                            top: Sizer.height(16),
                            bottom: Sizer.height(100),
                          ),
                          children: [
                            selectedNotificationType ==
                                    NotificationType.transactions
                                ? const TransactionNotification()
                                : const ActivitiesNotification(),
                          ],
                        );
                      }),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
