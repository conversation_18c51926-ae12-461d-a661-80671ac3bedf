import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class TradeOrderOfCollectionScreen extends StatefulWidget {
  const TradeOrderOfCollectionScreen({super.key});

  @override
  State<TradeOrderOfCollectionScreen> createState() =>
      _TradeOrderOfCollectionScreenState();
}

class _TradeOrderOfCollectionScreenState
    extends State<TradeOrderOfCollectionScreen> {
  bool isSwitchedToTrade = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Trade Order Of Collection',
            subtitle:
                "You can trade your order of collection with \nsomeone else for a little amount of fee",
          ),
          const YBox(80),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'Trade your order of collection with IK008',
                  style: AppTypography.text18.copyWith(
                    color: AppColors.neutral400,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextSpan(
                  text: '(September collector)',
                  style: AppTypography.text14.copyWith(
                    color: AppColors.neutral200,
                  ),
                ),
              ],
            ),
          ),
          const YBox(32),
          const CustomTextField(
            // controller: passwordC,
            // focusNode: passwordF,
            labelText: 'Enter trade details',
            showLabelHeader: true,
          ),
          const YBox(4),
          Text(
            'Little charge apply',
            style: AppTypography.text13.copyWith(
              color: AppColors.neutral200,
            ),
          ),
          const YBox(120),
          CustomBtn.solid(
            text: 'Accept',
            textStyle: AppTypography.text16.copyWith(
              color: AppColors.white,
            ),
            onTap: () {},
          ),
          const YBox(16),
          Text(
            'Stakeholders only have the opportunity of \ntrading three times',
            textAlign: TextAlign.center,
            style: AppTypography.text13.copyWith(
              color: AppColors.neutral200,
            ),
          ),
          const YBox(100),
        ],
      ),
    );
  }
}
