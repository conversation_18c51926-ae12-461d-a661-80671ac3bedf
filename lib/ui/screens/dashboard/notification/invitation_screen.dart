import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class InvitationScreen extends StatefulWidget {
  const InvitationScreen({super.key});

  @override
  State<InvitationScreen> createState() => _InvitationScreenState();
}

class _InvitationScreenState extends State<InvitationScreen> {
  InvitationType invitationType = InvitationType.accepted;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'First Invitation Result',
            subtitle: "Unique Brothers (10 Members)",
          ),
          const YBox(30),
          Row(
            children: [
              Expanded(
                child: MiniBtn(
                  text: 'Accepted',
                  isSelected: invitationType == InvitationType.accepted,
                  onTap: () {
                    invitationType = InvitationType.accepted;
                    setState(() {});
                  },
                ),
              ),
              const XBox(20),
              Expanded(
                child: MiniBtn(
                  text: 'Rejected',
                  isSelected: invitationType == InvitationType.rejected,
                  onTap: () {
                    invitationType = InvitationType.rejected;
                    setState(() {});
                  },
                ),
              )
            ],
          ),
          const YBox(30),
          if (invitationType == InvitationType.accepted)
            AcceptedInvitationTab(company: CompanyModel()),
          if (invitationType == InvitationType.rejected)
            const RejectedInvitationTab(),
        ],
      ),
    );
  }
}
