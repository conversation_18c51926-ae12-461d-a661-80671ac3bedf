import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  TextEditingController newPasswordC = TextEditingController();
  TextEditingController confirmPasswordC = TextEditingController();
  FocusNode newPasswordF = FocusNode();
  FocusNode confirmPasswordF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    confirmPasswordC.dispose();
    newPasswordC.dispose();
    newPasswordF.dispose();
    confirmPasswordF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Change Password',
            subtitle:
                "Your new password must be different from \npreviously used passwords",
          ),
          const YBox(48),
          CustomTextField(
            controller: newPasswordC,
            focusNode: newPasswordF,
            labelText: 'Old Password',
            showLabelHeader: true,
            isPassword: true,
          ),
          const YBox(30),
          CustomTextField(
            controller: newPasswordC,
            focusNode: newPasswordF,
            labelText: 'New Password',
            showLabelHeader: true,
            isPassword: true,
          ),
          const YBox(30),
          CustomTextField(
            controller: confirmPasswordC,
            focusNode: confirmPasswordF,
            labelText: 'Confirm Password',
            showLabelHeader: true,
            isPassword: true,
          ),
          const YBox(4),
          Text(
            'Password must contain at least 8 characters, one upper case, a number & one special character',
            style: AppTypography.text12.copyWith(color: AppColors.neutral200),
          ),
          const YBox(60),
          CustomBtn.solid(
            text: 'Change Password',
            onTap: () {
              Navigator.pushNamed(context, RoutePath.lockedScreen);
            },
          ),
        ],
      ),
    );
  }
}
