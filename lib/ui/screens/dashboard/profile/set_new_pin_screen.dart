import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class SetNewPinScreen extends ConsumerStatefulWidget {
  const SetNewPinScreen({super.key});

  @override
  ConsumerState<SetNewPinScreen> createState() => _SetNewPinScreenState();
}

class _SetNewPinScreenState extends ConsumerState<SetNewPinScreen> {
  final pinC = TextEditingController();
  final pinF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinF);
    pinF.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    pinC.dispose();
    pinF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final profileRef = ref.watch(profileVm);
      return Scaffold(
        backgroundColor: AppColors.purpleF1,
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(20),
          ),
          children: [
            const YBox(160),
            CustomPinWidget(
              controller: pinC,
              pinFocusNode: pinF,
              title: 'Set a new PIN',
              subtitle: 'Set a 4 digit pin to make your wallet more secure',
            ),
            const YBox(210),
            CustomBtn.solid(
              text: 'Set Pin',
              onTap: () {
                profileRef.newPin = pinC.text;
                Navigator.pushNamed(context, RoutePath.repeatPinScreen);
              },
            ),
            const YBox(40),
          ],
        ),
      );
    });
  }
}
