import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class HelpSupportScreen extends StatefulWidget {
  const HelpSupportScreen({super.key});

  @override
  State<HelpSupportScreen> createState() => _HelpSupportScreenState();
}

class _HelpSupportScreenState extends State<HelpSupportScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Help & Support',
            subtitle: 'Our crew are standing by for service \nand support',
          ),
          const YBox(50),
          ProfileListTile(
            title: 'FAQs',
            subtitle: 'Find answers to your questions',
            icon: Iconsax.message_question,
            onTap: () {
              // Navigator.pushNamed(context, RoutePath.faqScreen);
              Navigator.pushNamed(
                context,
                RoutePath.customWebviewScreen,
                arguments: WebViewArg(
                  webURL: AppConst.faq,
                ),
              );
            },
          ),
          const YBox(30),
          ProfileListTile(
            title: 'Call',
            subtitle: 'Talk to our representative',
            icon: Iconsax.call,
            onTap: () {
              Navigator.pushNamed(context, RoutePath.contactScreen);
            },
          ),
          const YBox(30),
          ProfileListTile(
            title: 'Email',
            subtitle: 'Get a solution directly to your email',
            icon: Iconsax.sms,
            onTap: () {},
          ),
        ],
      ),
    );
  }
}
