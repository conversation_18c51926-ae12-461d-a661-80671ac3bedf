import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class TransactionHistoryScreen extends StatelessWidget {
  const TransactionHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(
        headerText: 'Transaction History',
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
        child: Column(
          children: [
            const YBox(24),
            CustomTextField(
              // controller: dobC,
              // focusNode: dobF,
              hintText: 'Search contributions...',

              isReadOnly: true,
              prefixIcon: Padding(
                padding: EdgeInsets.all(Sizer.radius(10)),
                child: SvgPicture.asset(AppSvgs.search),
              ),
              suffixIcon: InkWell(
                onTap: () {
                  ModalWrapper.bottomSheet(
                    context: context,
                    widget: const SelectionModal(
                      title: 'Search By:',
                      selections: ['Withdrawals', 'Credit', 'Date'],
                      selectionColor: AppColors.pri400,
                    ),
                  );
                },
                child: const Icon(
                  Iconsax.setting_4,
                  color: AppColors.neutral200,
                ),
              ),
            ),
            Expanded(
              child: ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.only(
                  top: Sizer.height(24),
                  bottom: Sizer.height(100),
                ),
                itemBuilder: (ctx, i) {
                  return TransactionListTile(
                    onTap: () {
                      ModalWrapper.showCustomDialog(
                        context,
                        child: const TransactionDetailModal(),
                      );
                    },
                  );
                },
                separatorBuilder: (_, __) => const YBox(24),
                itemCount: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
