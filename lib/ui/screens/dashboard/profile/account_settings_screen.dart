import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class AccountSettingsScreen extends ConsumerStatefulWidget {
  const AccountSettingsScreen({super.key});

  @override
  ConsumerState<AccountSettingsScreen> createState() =>
      _AccountSettingsScreenState();
}

class _AccountSettingsScreenState extends ConsumerState<AccountSettingsScreen> {
  bool _useFaceId = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final profileRef = ref.watch(profileVm);
        return Scaffold(
          backgroundColor: AppColors.purpleF1,
          appBar: const CustomHeader(),
          body: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(20),
              const CustomSubHeader(
                title: 'Account Settings',
              ),
              const YBox(56),
              ProfileListTile(
                title: 'Use Fingerprint or Face ID',
                icon: Iconsax.finger_scan,
                titleStyle: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral300,
                ),
                onTap: () {},
                trailingWidget: CustomSwitch(
                  value: _useFaceId,
                  onChanged: (value) {
                    updateFingerPrint(value);
                  },
                ),
              ),
              const YBox(30),
              ProfileListTile(
                title: 'Change Password',
                icon: Iconsax.lock_1,
                titleStyle: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral300,
                ),
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.changePasswordScreen);
                },
              ),
              const YBox(30),
              ProfileListTile(
                title: 'Change PIN',
                icon: AppSvgs.shieldSecurity,
                titleStyle: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral300,
                ),
                onTap: () {
                  if (profileRef.authUser?.hasTransactionPin != null) {
                    Navigator.pushNamed(context, RoutePath.changePinScreen);
                  } else {
                    Navigator.pushNamed(context, RoutePath.setNewPinScreen);
                  }
                },
              ),
              const YBox(30),
              ProfileListTile(
                title: 'Debit Card',
                icon: Iconsax.card_add,
                titleStyle: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral300,
                ),
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.debitCardScreen,
                  );
                },
              ),
              const YBox(30),
              ProfileListTile(
                title: 'Bank Account',
                icon: Iconsax.bank,
                titleStyle: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral300,
                ),
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.bankAccountScreen,
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> updateFingerPrint(bool isEnabled) async {
    if (isEnabled) {
      final isAuthenticated = await BiometricService.authenticate();
      if (isAuthenticated) {
        await StorageService.storeBoolItem(
            StorageKey.fingerPrintIsEnabled, true);
        _useFaceId = true;
        setState(() {});
      } else {
        FlushBarToast.fLSnackBar(
          message: "Biometric Authentication Failed",
        );
      }
    } else {
      await StorageService.removeItem(StorageKey.fingerPrintIsEnabled);
      _useFaceId = false;
      setState(() {});
    }
  }
}
