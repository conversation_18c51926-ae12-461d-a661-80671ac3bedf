import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class RepeatPinScreen extends ConsumerStatefulWidget {
  const RepeatPinScreen({super.key});

  @override
  ConsumerState<RepeatPinScreen> createState() => _RepeatPinScreenState();
}

class _RepeatPinScreenState extends ConsumerState<RepeatPinScreen> {
  final pinC = TextEditingController();
  final pinF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinF);
    pinF.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    pinC.dispose();
    pinF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final profileRef = ref.watch(profileVm);
      return Scaffold(
        backgroundColor: AppColors.purpleF1,
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(20),
          ),
          children: [
            const YBox(160),
            Form(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: CustomPinWidget(
                pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                controller: pinC,
                pinFocusNode: pinF,
                title: 'Repeat PIN',
                subtitle: 'Set a 4 digit pin to make your wallet more secure',
                validator: (currentText) {
                  if (currentText == profileRef.newPin) {
                    return null;
                  } else {
                    return 'PINs do not match';
                  }
                },
                onChanged: (currentText) {
                  profileRef.pinConfirm = currentText;
                },
              ),
            ),
            Visibility(
                replacement: SizedBox(
                  height: 210.h,
                  child: const Center(
                    child: CircularProgressIndicator.adaptive(),
                  ),
                ),
                visible: !profileRef.isBusy,
                child: const YBox(210)),
            CustomBtn.solid(
              online: profileRef.canChangePin && !profileRef.isBusy,
              text: 'Set Pin',
              onTap: () {
                profileRef.proceedToPinChange(true).then((value) {
                  handleApiResponse(
                    response: value,
                    onCompleted: () {
                      Navigator.pushNamedAndRemoveUntil(
                          context,
                          RoutePath.dashboardNavigationScreen,
                          (route) => false);
                    },
                  );
                });
              },
            ),
            const YBox(40),
          ],
        ),
      );
    });
  }
}
