import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:flutter/services.dart';

class ContactScreen extends StatefulWidget {
  const ContactScreen({super.key});

  @override
  State<ContactScreen> createState() => _ContactScreenState();
}

class _ContactScreenState extends State<ContactScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Contact',
            subtitle: 'Our crew are standing by for service \nand support',
          ),
          const YBox(44),
          ContactTile(
            contact: '08023232435',
            onCopy: () async {
              await Clipboard.setData(const ClipboardData(text: '08023232435'));
              FlushBarToast.fLSnackBar(
                snackBarType: SnackBarType.success,
                duration: 3,
                message: 'Copied to clipboard',
              );
            },
          ),
          const YBox(16),
          ContactTile(
            contact: '08023232435',
            onCopy: () {},
          ),
          const YBox(16),
          ContactTile(
            contact: '08023232435',
            onCopy: () {},
          ),
          const YBox(30),
          Text(
            'Email',
            style: AppTypography.text14.copyWith(
              color: AppColors.neutral200,
            ),
          ),
          const YBox(4),
          ContactTile(
            contact: '<EMAIL>',
            onCopy: () {},
          ),
          const YBox(40),
          Text(
            'Connect with us',
            style: AppTypography.text14.copyWith(
              color: AppColors.neutral400,
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(12),
          ProfileListTile(
            title: 'Twitter',
            icon: AppSvgs.x,
            onTap: () {
              Navigator.pushNamed(context, RoutePath.faqScreen);
            },
          ),
          const YBox(24),
          ProfileListTile(
            title: 'Facebook',
            icon: AppSvgs.fb,
            onTap: () {
              Navigator.pushNamed(context, RoutePath.faqScreen);
            },
          ),
          const YBox(24),
          ProfileListTile(
            title: 'Instagram',
            icon: AppSvgs.ig,
            onTap: () {
              Navigator.pushNamed(context, RoutePath.faqScreen);
            },
          ),
          const YBox(24),
          ProfileListTile(
            title: 'LinkedIn',
            icon: AppSvgs.ln,
            onTap: () {
              Navigator.pushNamed(context, RoutePath.faqScreen);
            },
          ),
        ],
      ),
    );
  }
}

class ContactTile extends StatelessWidget {
  const ContactTile({
    super.key,
    required this.contact,
    this.onCopy,
  });

  final String contact;
  final Function()? onCopy;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          contact,
          style: AppTypography.text16.copyWith(
            color: AppColors.neutral400,
          ),
        ),
        const Spacer(),
        InkWell(
          onTap: onCopy,
          child: const Icon(
            Iconsax.document_copy,
            color: AppColors.neutral200,
          ),
        )
      ],
    );
  }
}
