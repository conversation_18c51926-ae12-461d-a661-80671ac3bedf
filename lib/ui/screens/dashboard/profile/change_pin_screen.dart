import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class ChangePinScreen extends ConsumerStatefulWidget {
  const ChangePinScreen({super.key});

  @override
  ConsumerState<ChangePinScreen> createState() => _ChangePinScreenState();
}

class _ChangePinScreenState extends ConsumerState<ChangePinScreen> {
  final pinC = TextEditingController();
  final pinF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinF);
    pinF.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    pinC.dispose();
    pinF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final profileRef = ref.watch(profileVm);
      return Scaffold(
        backgroundColor: AppColors.purpleF1,
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(20),
          ),
          children: [
            const YBox(160),
            CustomPinWidget(
              controller: pinC,
              pinFocusNode: pinF,
              title: 'Enter your current PIN',
              subtitle: 'Set a 4 digit pin to make your wallet more secure',
            ),
            const YBox(210),
            CustomBtn.solid(
              text: 'Set Pin',
              onTap: () {
                profileRef.oldPin = pinC.text;
                Navigator.pushNamed(context, RoutePath.setNewPinScreen);
              },
            ),
            const YBox(40),
          ],
        ),
      );
    });
  }
}
