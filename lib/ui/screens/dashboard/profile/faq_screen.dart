import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class FaqScreen extends StatefulWidget {
  const FaqScreen({super.key});

  @override
  State<FaqScreen> createState() => _FaqScreenState();
}

class _FaqScreenState extends State<FaqScreen> {
  int currentIndex = -1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: Column(
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Frequently Asked Questions',
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
                bottom: Sizer.width(100),
              ),
              children: [
                const YBox(16),
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding:
                        EdgeInsets.symmetric(horizontal: (Sizer.height(24))),
                    itemBuilder: (context, index) {
                      return FAQAccordion(
                        title: 'What is Equalcash contribution?',
                        descrption:
                            'Daily contribution refers to the practice of consistently saving small amounts of money on a regular basis in order to build savings over time. It is a simple, automated way to prepare financially for the future, whether saving up for personal goals, growing a business, or ensuring funds are available to seize opportunities.',
                        isExpanded: currentIndex == index,
                        onTap: () {
                          setState(() {
                            currentIndex = currentIndex == index ? -1 : index;
                          });
                        },
                      );
                    },
                    separatorBuilder: (context, index) => const Divider(
                      color: AppColors.purpleF1,
                    ),
                    itemCount: 10,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class FAQAccordion extends StatelessWidget {
  const FAQAccordion({
    super.key,
    required this.title,
    required this.descrption,
    this.isExpanded = false,
    this.onTap,
  });

  final String title;
  final String descrption;
  final bool isExpanded;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          // horizontal: Sizer.width(10),
          vertical: Sizer.height(20),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(
            Sizer.radius(12),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.neutral400,
                  ),
                ),
                const Spacer(),
                AnimatedRotation(
                  duration: const Duration(milliseconds: 500),
                  turns: isExpanded ? 0.5 : 0,
                  child: const Icon(
                    Iconsax.arrow_down_1,
                  ),
                ),
              ],
            ),
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              child: isExpanded
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const YBox(16),
                        Text(
                          descrption,
                          style: AppTypography.text14.copyWith(
                              color: AppColors.neutral400, height: 1.5),
                        ),
                      ],
                    )
                  : const SizedBox.shrink(),
            )
          ],
        ),
      ),
    );
  }
}
