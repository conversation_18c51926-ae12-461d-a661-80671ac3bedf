import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final profileRef = ref.watch(profileVm);
        return Scaffold(
            backgroundColor: AppColors.primaryPurple,
            body: Stack(
              children: [
                SizedBox(
                  width: Sizer.screenWidth,
                  height: Sizer.screenHeight,
                  child: Column(
                    children: [
                      const YBox(60),
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(16),
                        ),
                        child: Row(
                          children: [
                            Text(
                              'Profile',
                              style: AppTypography.text20.copyWith(
                                color: AppColors.secondary50,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            InkWell(
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  RoutePath.editProfileScreen,
                                );
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical: Sizer.height(10),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Iconsax.edit,
                                      color: AppColors.purpleC9,
                                      size: Sizer.radius(16),
                                    ),
                                    const XBox(6),
                                    Text(
                                      'Edit Profile',
                                      style: AppTypography.text14.copyWith(
                                        color: AppColors.purpleC9,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const YBox(80),
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(16),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.purpleF1,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(
                                Sizer.radius(24),
                              ),
                              topRight: Radius.circular(
                                Sizer.radius(24),
                              ),
                            ),
                          ),
                          child: Column(
                            children: [
                              const YBox(130),
                              ProfileListTile(
                                title: 'Transactions History',
                                subtitle: 'See details about your deposits',
                                icon: Iconsax.document_text,
                                onTap: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.transactionHistoryScreen,
                                  );
                                },
                              ),
                              const YBox(30),
                              ProfileListTile(
                                title: 'Account Settings',
                                subtitle: 'Keep your account safe',
                                icon: Iconsax.key_square,
                                onTap: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.accountSettingsScreen,
                                  );
                                },
                              ),
                              const YBox(30),
                              ProfileListTile(
                                title: 'Help & Support',
                                subtitle: 'Have an issue? Speak to our team',
                                icon: Iconsax.info_circle,
                                onTap: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.helpSupportScreen,
                                  );
                                },
                              ),
                              const YBox(30),
                              ProfileListTile(
                                title: 'About',
                                subtitle: 'Information about DailyPay',
                                icon: AppSvgs.menu,
                                onTap: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.customWebviewScreen,
                                    arguments: WebViewArg(
                                      webURL: AppConst.about,
                                    ),
                                  );
                                },
                              ),
                              const YBox(30),
                              Consumer(
                                builder: (context, ref, child) {
                                  return ProfileListTile(
                                    title: 'Logout',
                                    subtitle: 'Logout of your account',
                                    icon: Iconsax.logout,
                                    onTap: () {
                                      ModalWrapper.bottomSheet(
                                        context: context,
                                        widget: LogoutModal(
                                          onAction: () {
                                            ref.read(loginVm).logout();
                                          },
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Positioned(
                  top: Sizer.height(120),
                  left: 0,
                  right: 0,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const AvatarWidget(),
                      const YBox(8),
                      Text(
                        profileRef.fullName,
                        style: AppTypography.text16.copyWith(
                          fontWeight: FontWeight.w400,
                          color: AppColors.neutral400,
                        ),
                      ),
                      const YBox(4),
                      Text(
                        'IKOO8',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral200,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ));
      },
    );
  }
}
