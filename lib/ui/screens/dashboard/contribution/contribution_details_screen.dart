import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class ContributionDetailsScreen extends StatefulWidget {
  const ContributionDetailsScreen({
    super.key,
    required this.company,
  });

  final CompanyModel company;

  @override
  State<ContributionDetailsScreen> createState() =>
      _ContributionDetailsScreenState();
}

class _ContributionDetailsScreenState extends State<ContributionDetailsScreen> {
  ContributionDetailsType contributionDetailsType =
      ContributionDetailsType.cashSheet;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
        child: ListView(
          children: [
            const YBox(16),
            Center(
              child: CustomSubHeader(
                title: '${widget.company.name}',
                subtitle:
                    'Created by ${widget.company.ceo?.firstName ?? ''} ${widget.company.ceo?.lastName ?? ''}',
              ),
            ),
            const YBox(20),
            Row(
              children: [
                Expanded(
                  child: MiniBtn(
                    text: 'Cash Sheet',
                    isSelected: contributionDetailsType ==
                        ContributionDetailsType.cashSheet,
                    onTap: () {
                      contributionDetailsType =
                          ContributionDetailsType.cashSheet;
                      setState(() {});
                    },
                  ),
                ),
                const XBox(20),
                Expanded(
                  child: MiniBtn(
                    text: 'Members',
                    isSelected: contributionDetailsType ==
                        ContributionDetailsType.members,
                    onTap: () {
                      contributionDetailsType = ContributionDetailsType.members;
                      setState(() {});
                    },
                  ),
                )
              ],
            ),
            const YBox(30),
            if (contributionDetailsType == ContributionDetailsType.cashSheet)
              CashSheetTable(company: widget.company),
            if (contributionDetailsType == ContributionDetailsType.members)
              AcceptedInvitationTab(company: widget.company),
            const YBox(100),
          ],
        ),
      ),
    );
  }
}
