import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class ContributionScreen extends StatefulWidget {
  const ContributionScreen({super.key});

  @override
  State<ContributionScreen> createState() => _ContributionScreenState();
}

class _ContributionScreenState extends State<ContributionScreen> {
  ContributionType contributionType = ContributionType.create;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomDashboardAppbar(
        height: 100,
      ),
      body: Container(
        color: AppColors.purpleF1,
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          children: [
            const YBox(16),
            Row(
              children: [
                Expanded(
                  child: MiniBtn(
                    text: 'Created',
                    isSelected: contributionType == ContributionType.create,
                    onTap: () {
                      contributionType = ContributionType.create;
                      setState(() {});
                    },
                  ),
                ),
                const XBox(20),
                Expanded(
                  child: MiniBtn(
                    text: 'Joined',
                    isSelected: contributionType == ContributionType.join,
                    onTap: () {
                      contributionType = ContributionType.join;
                      setState(() {});
                    },
                  ),
                )
              ],
            ),
            const YBox(8),
            if (contributionType == ContributionType.create)
              const CreatedCompanyTab(),
            if (contributionType == ContributionType.join)
              const JoinedCompanyTab(),
          ],
        ),
      ),
    );
  }
}
