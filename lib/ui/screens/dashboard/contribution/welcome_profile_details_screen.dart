import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class WelcomeProfileDetailsScreen extends ConsumerStatefulWidget {
  const WelcomeProfileDetailsScreen({
    super.key,
    required this.company,
  });

  final CompanyModel company;

  @override
  ConsumerState<WelcomeProfileDetailsScreen> createState() =>
      _WelcomeProfileDetailsScreenState();
}

class _WelcomeProfileDetailsScreenState
    extends ConsumerState<WelcomeProfileDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    final profileRef = ref.watch(profileVm);
    return BusyOverlay(
      show: ref.watch(companyVmodel).isBusy,
      child: Scaffold(
        backgroundColor: AppColors.purpleF1,
        appBar: const CustomHeader(),
        body: RefreshIndicator(
          onRefresh: () async {
            await profileRef.getUser();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(20),
              Column(
                children: [
                  Text(
                    "Welcome!",
                    style: AppTypography.text24.copyWith(
                      color: AppColors.neutral400,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(6),
                  RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: "You are about to join ",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.neutral200,
                            height: 1.5,
                          ),
                        ),
                        TextSpan(
                          text: widget.company.name,
                          style: AppTypography.text14.copyWith(
                            color: AppColors.neutral200,
                            height: 1.5,
                            fontWeight: FontWeight
                                .bold, // Different style for company name
                          ),
                        ),
                        TextSpan(
                          text: ".\nPlease confirm the information below",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.neutral200,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
              const YBox(30),
              CustomTextField(
                labelText: 'First Name',
                hintText: profileRef.authUser?.firstName ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              CustomTextField(
                labelText: 'Email',
                hintText: profileRef.authUser?.email ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              CustomTextField(
                labelText: 'Phone Number',
                hintText: profileRef.authUser?.phone ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              CustomTextField(
                labelText: 'Address',
                hintText: profileRef.authUser?.address ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(80),
              CustomBtn.solid(
                text: 'Join Company',
                textStyle: AppTypography.text16.copyWith(
                  color: AppColors.white,
                ),
                onTap: () async {
                  final res = await ref.read(companyVmodel).joinCompany(
                        companyId: widget.company.id ?? 0,
                      );
                  handleApiResponse(
                    response: res,
                    onCompleted: () {
                      ModalWrapper.bottomSheet(
                        context: context,
                        widget: InfoActionModal(
                          title: 'Company Joined Successfully',
                          content:
                              'To proceed and create a contribution company, please complete the KYC',
                          btnText: 'Proceed To Dashboard',
                          onTapSoldBtn: () {
                            Navigator.pushNamedAndRemoveUntil(
                              NavKey.appNavigatorKey.currentContext!,
                              RoutePath.dashboardNavigationScreen,
                              (r) => false,
                            );
                          },
                        ),
                      );
                    },
                  );
                },
              ),
              const YBox(40),
            ],
          ),
        ),
      ),
    );
  }
}
