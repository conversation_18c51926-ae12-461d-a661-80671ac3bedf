import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(companyVmodel).getAllCompanies();
    });
  }

  @override
  Widget build(BuildContext context) {
    final companyRef = ref.watch(companyVmodel);
    final profileRef = ref.watch(profileVm);
    return SizedBox(
      height: Sizer.screenHeight,
      width: Sizer.screenWidth,
      child: BusyOverlay(
        show: ref.watch(profileVm).isBusy,
        child: Scaffold(
          appBar: CustomDashboardAppbar(
            topWidget: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Hello, ${profileRef.fullName}',
                        style: AppTypography.text16.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const YBox(2),
                      Text(
                        'Good ${AppUtils.getGreeting()}!',
                        style: AppTypography.text14
                            .copyWith(color: AppColors.white.withOpacity(0.7)),
                      ),
                    ],
                  ),
                ),
                Text(
                  AppUtils.daymy(DateTime.now()),
                  style: AppTypography.text14
                      .copyWith(color: AppColors.white.withOpacity(0.7)),
                ),
              ],
            ),
          ),
          body: Container(
            color: AppColors.purpleF1,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: LoadableContentBuilder(
              isBusy: companyRef.isBusy,
              items: companyRef.allCompanies,
              loadingBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: Sizer.height(16),
                    bottom: Sizer.height(100),
                  ),
                  itemBuilder: (ctx, i) {
                    return const Skeletonizer(
                      enabled: true,
                      child: HomeCashBucketCard(),
                    );
                  },
                  separatorBuilder: (_, __) => const YBox(16),
                  itemCount: 8,
                );
              },
              emptyBuilder: (context) {
                return Center(
                  child: Text(
                    "No companies found",
                    style: AppTypography.text18.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.neutral300,
                    ),
                  ),
                );
              },
              contentBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: Sizer.height(16),
                    bottom: Sizer.height(100),
                  ),
                  itemBuilder: (ctx, i) {
                    return HomeCashBucketCard(
                      company: companyRef.allCompanies[i],
                    );
                  },
                  separatorBuilder: (_, __) => const YBox(16),
                  itemCount: companyRef.allCompanies.length,
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
