import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/services.dart';

class StartCompanyScreen extends ConsumerStatefulWidget {
  const StartCompanyScreen({super.key});

  @override
  ConsumerState<StartCompanyScreen> createState() => _StartCompanyScreenState();
}

class _StartCompanyScreenState extends ConsumerState<StartCompanyScreen> {
  final countryPicker = const FlCountryCodePicker(
    title: SizedBox.shrink(),
  );
  final companyNameC = TextEditingController();
  final companyTypeC = TextEditingController();
  final cashBucketC = TextEditingController();
  final cashDropC = TextEditingController();
  final commencementDateC = TextEditingController();
  final numberOfStakeholdersC = TextEditingController();
  final durationC = TextEditingController();

  final companyNameFocus = FocusNode();
  final companyTypeFocus = FocusNode();
  final cashBucketFocus = FocusNode();
  final cashDropFocus = FocusNode();
  final commencementDateFocus = FocusNode();
  final numberOfStakeholdersFocus = FocusNode();
  final durationFocus = FocusNode();

  DateTime? _commencementDate;
  int numberOfStakeholders = 1;
  // String? _selectedDuration;
  // String? _selectedStakeholderCount;
  // String? _selectedType;

  List<String> _members = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    companyNameC.dispose();
    companyTypeC.dispose();
    cashBucketC.dispose();
    cashDropC.dispose();
    commencementDateC.dispose();
    numberOfStakeholdersC.dispose();
    durationC.dispose();

    companyNameFocus.dispose();
    companyTypeFocus.dispose();
    cashBucketFocus.dispose();
    cashDropFocus.dispose();
    commencementDateFocus.dispose();
    numberOfStakeholdersFocus.dispose();
    durationFocus.dispose();

    super.dispose();
  }

  bool get isFormValid =>
      companyNameC.text.isNotEmpty &&
      cashBucketC.text.isNotEmpty &&
      numberOfStakeholdersC.text.isNotEmpty &&
      companyTypeC.text.isNotEmpty &&
      cashDropC.text.isNotEmpty &&
      commencementDateC.text.isNotEmpty &&
      durationC.text.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final companyRef = ref.watch(companyVmodel);
      return BusyOverlay(
        show: companyRef.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.purpleF1,
          appBar: const CustomHeader(),
          body: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(20),
              const CustomSubHeader(
                title: 'Start a Company',
                subtitle:
                    "Welcome back! Please login into your \nEqualcash account to continue",
              ),
              const YBox(40),
              CustomTextField(
                controller: companyNameC,
                focusNode: companyNameFocus,
                labelText: 'Company’s Name',
                showLabelHeader: true,
              ),
              const YBox(16),
              CustomTextField(
                controller: cashBucketC,
                focusNode: cashBucketFocus,
                keyboardType: KeyboardType.number,
                labelText: 'Cash Bucket ',
                optionalText: '(Targeted Amount)',
                showLabelHeader: true,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  FilteringTextInputFormatter.singleLineFormatter
                ],
                onChanged: (p0) {
                  if (p0.isNotEmpty && numberOfStakeholdersC.text.isNotEmpty) {
                    setState(() {
                      cashDropC.text =
                          ((int.tryParse(p0) ?? 0) / numberOfStakeholders)
                              .toStringAsFixed(0);
                    });
                  }
                },
              ),
              const YBox(16),
              // You (1) + numberOfStakeholdersC
              CustomTextField(
                controller: numberOfStakeholdersC,
                focusNode: numberOfStakeholdersFocus,
                isReadOnly: true,
                labelText: "Number of Stakeholders",
                showLabelHeader: true,
                optionalText: "(You included)",
                prefixIcon: Padding(
                  padding: EdgeInsets.only(
                    left: Sizer.width(10),
                    top: Sizer.width(10),
                  ),
                  child: Text(
                    "You +  ",
                    style: AppTypography.text16.copyWith(
                      color: AppColors.neutral200,
                    ),
                  ),
                ),
                suffixIcon: Icon(
                  Iconsax.arrow_down_1,
                  size: Sizer.radius(24),
                ),
                onTap: () async {
                  if (cashBucketC.text.isEmpty) {
                    return FlushBarToast.fLSnackBar(
                      snackBarType: SnackBarType.warning,
                      message: "Please enter the cash bucket first",
                    );
                  }
                  final result = await ModalWrapper.bottomSheet(
                    context: context,
                    widget: SelectionModal(
                      title: 'Number of Stakeholders',
                      maxHeight: Sizer.screenHeight * 0.6,
                      selections:
                          List.generate(6, (index) => (index + 1).toString()),
                    ),
                  );
                  if (result != null && context.mounted) {
                    setState(() {
                      final totalR = ((int.tryParse(result ?? '0') ?? 0));
                      numberOfStakeholders = totalR + 1; // Added you

                      numberOfStakeholdersC.text = totalR.toString();
                      durationC.text = numberOfStakeholders.toString();
                      cashDropC.text = ((int.tryParse(cashBucketC.text) ?? 0) /
                              numberOfStakeholders)
                          .toStringAsFixed(0);
                    });
                  }
                },
              ),
              const YBox(16),
              CustomTextField(
                controller: companyTypeC,
                focusNode: companyTypeFocus,
                isReadOnly: true,
                labelText: 'Company Type ',
                showLabelHeader: true,
                suffixIcon: Icon(
                  Iconsax.arrow_down_1,
                  size: Sizer.radius(24),
                ),
                onTap: () async {
                  final result = await ModalWrapper.bottomSheet(
                    context: context,
                    widget: const SelectionModal(
                      title: 'Company Type',
                      selections: ["Open", "Closed"],
                    ),
                  );
                  if (result is String && context.mounted) {
                    setState(() {
                      companyTypeC.text = result;
                    });
                  }
                },
              ),
              const YBox(16),
              // cash = cash bucket/no stalk holders
              CustomTextField(
                controller: cashDropC,
                focusNode: cashDropFocus,
                labelText: 'Cash Drop',
                optionalText: '(Contribution per person)',
                showLabelHeader: true,
                isReadOnly: true,
                keyboardType: KeyboardType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  FilteringTextInputFormatter.singleLineFormatter
                ],
              ),
              const YBox(16),
              CustomTextField(
                controller: commencementDateC,
                focusNode: commencementDateFocus,
                isReadOnly: true,
                labelText: "Commencement Date",
                showLabelHeader: true,
                suffixIcon: Icon(
                  Iconsax.calendar_1,
                  size: Sizer.radius(24),
                ),
                onTap: () {
                  CustomCupertinoDatePicker(
                    context: context,
                    minimumDate: DateTime.now(),
                    onDateTimeChanged: (dateTime) {
                      _commencementDate = dateTime;
                      commencementDateC.text =
                          AppUtils.dayWithSuffixMonthAndYear(
                              _commencementDate ?? DateTime.now());
                      setState(() {});
                    },
                  ).show();
                },
              ),

              const YBox(16),
              CustomTextField(
                controller: durationC,
                focusNode: durationFocus,
                isReadOnly: true,
                labelText: 'Duration ',
                showLabelHeader: true,
                suffixIcon: Icon(
                  Iconsax.arrow_down_1,
                  size: Sizer.radius(24),
                ),
              ),
              const YBox(16),
              InviteChipField(
                labelText: 'Invite by phone number or email',
                max: int.tryParse(numberOfStakeholdersC.text) ?? 1,
                onInviteesChanged: (invitees) {
                  setState(() {
                    _members = invitees;
                  });
                },
              ),
              const YBox(60),
              CustomBtn.solid(
                text: 'Continue',
                online: isFormValid,
                isLoading: companyRef.isBusy,
                onTap: () async {
                  final r = await ref.read(companyVmodel).createCompany(
                        name: companyNameC.text,
                        cashBucket: cashBucketC.text,
                        duration: numberOfStakeholders,
                        type: companyTypeC.text.toLowerCase(),
                        numberOfStakeholders: numberOfStakeholders,
                        commencementDate: AppUtils.formatToYmd(
                            _commencementDate ?? DateTime.now()),
                        members: _members,
                      );

                  handleApiResponse(
                    response: r,
                    onCompleted: () {
                      Navigator.pop(context);
                      ref.read(companyVmodel).getAllCompanies();
                    },
                  );
                },
              ),
              const YBox(100),
            ],
          ),
        ),
      );
    });
  }
}
