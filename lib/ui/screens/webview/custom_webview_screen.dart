import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CustomWebviewScreen extends StatefulWidget {
  const CustomWebviewScreen({
    super.key,
    required this.arg,
  });

  final WebViewArg arg;

  @override
  State<CustomWebviewScreen> createState() => _CustomWebviewScreenState();
}

class _CustomWebviewScreenState extends State<CustomWebviewScreen> {
  bool isLoading = true;

  late WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _initializeControllerFuture();
  }

  Future<void> _initializeControllerFuture() async {
    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(
            const PlatformWebViewControllerCreationParams());

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (_) async {
          isLoading = true;
        },
        onPageFinished: (finish) {
          isLoading = false;
          setState(() {});
        },
        onNavigationRequest: (NavigationRequest request) async {
          printty("urlpadhere ${request.url}");
          if (request.url.contains('success')) {
            // Add this line - prevents future callbacks from updating state
            // _controller = controller;

            if (widget.arg.onSucecess != null) {
              widget.arg.onSucecess!();
            }

            return NavigationDecision
                .prevent; // Prevent further navigation since we're leaving
          }
          return NavigationDecision.navigate;
        },
      ))
      ..loadRequest(Uri.parse(widget.arg.webURL));

    _controller = controller;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          // title: Text(widget.arg.appBarText),
          // centerTitle: true,
          ),
      body: Stack(
        children: [
          WebViewWidget(
            controller: _controller,
          ),
          isLoading ? const Center(child: LoaderIcon()) : const Stack(),
        ],
      ),
    );
  }
}
