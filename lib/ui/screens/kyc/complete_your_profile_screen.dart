import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:equalcash/ui/components/shared/textfields/validation/form_validator.dart';

class CompleteYourProfileScreen extends StatefulWidget {
  const CompleteYourProfileScreen({super.key});

  @override
  State<CompleteYourProfileScreen> createState() =>
      _CompleteYourProfileScreenState();
}

class _CompleteYourProfileScreenState extends State<CompleteYourProfileScreen>
    with FormValidationMixin {
  TextEditingController genderC = TextEditingController();
  TextEditingController occupationC = TextEditingController();
  TextEditingController addressC = TextEditingController();
  TextEditingController dobC = TextEditingController();

  FocusNode genderF = FocusNode();
  FocusNode occupationF = FocusNode();
  FocusNode addressF = FocusNode();
  FocusNode dobF = FocusNode();

  DateTime? selectedDate;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    genderC.dispose();
    occupationC.dispose();
    addressC.dispose();
    dobC.dispose();

    genderF.dispose();
    occupationF.dispose();
    addressF.dispose();
    dobF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Complete Your Profile',
            subtitle: "Please enter the information below",
          ),
          const YBox(40),
          CustomTextField(
            controller: genderC,
            focusNode: genderF,
            labelText: 'Gender',
            showLabelHeader: true,
            isReadOnly: true,
            validationRules: const [
              RequiredRule(fieldName: 'gender'),
            ],
            enableRealTimeValidation: true,
            suffixIcon: const Icon(
              Iconsax.arrow_down_1,
            ),
            onChanged: (v) {
              printty("gender changed: $v");
              setState(() {});
            },
            onTap: () async {
              final r = await ModalWrapper.bottomSheet(
                context: context,
                widget: const SelectionModal(
                    selections: ['Male', 'Female', 'Other']),
              );

              if (r is String && context.mounted) {
                genderC.text = r;
                setState(() {});
              }
            },
          ),
          const YBox(20),
          CustomTextField(
            controller: occupationC,
            focusNode: occupationF,
            labelText: 'Occupation',
            showLabelHeader: true,
            validationRules: ValidationRules.requiredWithLength(
              fieldName: 'occupation',
              minLength: 2,
              maxLength: 50,
              requiredMessage: 'Please enter your occupation',
              minLengthMessage: 'Occupation must be at least 2 characters',
              maxLengthMessage: 'Occupation must not exceed 50 characters',
            ),
            enableRealTimeValidation: true,
            onChanged: (v) => setState(() {}),
          ),
          const YBox(20),
          CustomTextField(
            controller: addressC,
            focusNode: addressF,
            labelText: 'Address',
            showLabelHeader: true,
            validationRules: ValidationRules.requiredWithLength(
              fieldName: 'address',
              minLength: 5,
              maxLength: 200,
              requiredMessage: 'Please enter your address',
              minLengthMessage: 'Address must be at least 5 characters',
              maxLengthMessage: 'Address must not exceed 200 characters',
            ),
            enableRealTimeValidation: true,
            onChanged: (v) => setState(() {}),
          ),
          const YBox(20),
          CustomTextField(
            controller: dobC,
            focusNode: dobF,
            labelText: 'Date of birth',
            showLabelHeader: true,
            isReadOnly: true,
            validationRules: const [
              RequiredRule(
                fieldName: 'date of birth',
                customErrorMessage: 'Please select your date of birth',
              ),
            ],
            enableRealTimeValidation: true,
            suffixIcon: const Icon(
              Iconsax.calendar_1,
            ),
            onChanged: (v) => setState(() {}),
            onTap: () {
              CustomCupertinoDatePicker(
                context: context,
                // Set initial date to 25 years ago (reasonable default for adults)
                initialDateTime:
                    DateTime.now().subtract(const Duration(days: 365 * 25)),
                // Allow dates from 100 years ago to 18 years ago (minimum age requirement)
                minimumDate:
                    DateTime.now().subtract(const Duration(days: 365 * 100)),
                maximumDate:
                    DateTime.now().subtract(const Duration(days: 365 * 18)),
                onDateTimeChanged: (dateTime) {
                  dobC.text = AppUtils.dayWithSuffixMonthAndYear(dateTime);
                  selectedDate = dateTime;
                  setState(() {});
                },
              ).show();
            },
          ),
          const YBox(60),
          CustomBtn.solid(
              text: 'Continue',
              onTap: () async {
                // Trigger validation on all fields using FormValidationMixin
                final isFormValid = await validateForm();

                if (isFormValid) {
                  // All fields are valid, proceed to next screen
                  // Navigator.pushNamed(context, RoutePath.verifyYourIdentityScreen);
                } else {
                  // Form has validation errors, errors are already displayed below fields
                  // User can see the error messages and fix the issues
                }
              }),
        ],
      ),
    );
  }
}
