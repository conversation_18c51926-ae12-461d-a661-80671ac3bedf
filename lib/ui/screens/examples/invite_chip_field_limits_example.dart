import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// Example screen to demonstrate the InviteChipField with max and min limits
class InviteChipFieldLimitsExample extends StatefulWidget {
  const InviteChipFieldLimitsExample({super.key});

  @override
  State<InviteChipFieldLimitsExample> createState() =>
      _InviteChipFieldLimitsExampleState();
}

class _InviteChipFieldLimitsExampleState
    extends State<InviteChipFieldLimitsExample> {
  final _formKey = GlobalKey<FormState>();
  final _maxLimitedFieldKey = GlobalKey<InviteChipFieldState>();
  final _minMaxLimitedFieldKey = GlobalKey<InviteChipFieldState>();

  List<String> _maxLimitedInvitees = [];
  List<String> _minMaxLimitedInvitees = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: AppBar(
        title: const Text('InviteChipField Limits'),
        backgroundColor: AppColors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.r),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              const CustomSubHeader(
                title: 'InviteChipField with Limits',
                subtitle: 'Demonstration of max and min parameters',
              ),
              const YBox(24),

              // Field with maximum limit
              Text(
                'Maximum Limit (3)',
                style: AppTypography.text16,
              ),
              const YBox(8),
              InviteChipField(
                key: _maxLimitedFieldKey,
                labelText: 'Invite (max 3)',
                hintText: 'Enter email or phone number',
                max: 3, // Maximum 3 invitees
                onInviteesChanged: (invitees) {
                  setState(() {
                    _maxLimitedInvitees = invitees;
                  });
                },
              ),

              const YBox(24),

              // Field with minimum and maximum limits
              Text(
                'Minimum (2) and Maximum (5) Limits',
                style: AppTypography.text16,
              ),
              const YBox(8),
              InviteChipField(
                key: _minMaxLimitedFieldKey,
                labelText: 'Invite (min 2, max 5)',
                hintText: 'Enter email or phone number',
                min: 2, // Minimum 2 invitees
                max: 5, // Maximum 5 invitees
                isRequired: true,
                onInviteesChanged: (invitees) {
                  setState(() {
                    _minMaxLimitedInvitees = invitees;
                  });
                },
              ),

              const YBox(24),

              // Instructions
              Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: AppColors.neutral300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions:',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const YBox(8),
                    Text(
                      '• First field has a maximum limit of 3 invitees',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                    const YBox(4),
                    Text(
                      '• Second field requires at least 2 invitees and allows up to 5',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                    const YBox(4),
                    Text(
                      '• Try adding more than the maximum to see the validation',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                  ],
                ),
              ),

              const YBox(32),

              // Validate button
              CustomBtn.solid(
                text: 'Validate Fields',
                onTap: () {
                  final isMaxFieldValid =
                      _maxLimitedFieldKey.currentState?.validate() ?? false;
                  final isMinMaxFieldValid =
                      _minMaxLimitedFieldKey.currentState?.validate() ?? false;

                  if (isMaxFieldValid && isMinMaxFieldValid) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('All fields are valid!'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                },
              ),

              const YBox(16),

              // Add sample data button
              CustomBtn.solid(
                text: 'Add Sample Data',
                onTap: () {
                  // Add sample data to the fields
                  final sampleEmails = [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                  ];

                  // Add 2 to the first field
                  setState(() {
                    _maxLimitedInvitees = sampleEmails.sublist(0, 2);
                    _minMaxLimitedInvitees = sampleEmails.sublist(0, 3);
                  });

                  // Update the fields
                  _maxLimitedFieldKey.currentState?.validate();
                  _minMaxLimitedFieldKey.currentState?.validate();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Sample data added'),
                    ),
                  );
                },
              ),

              const YBox(100),
            ],
          ),
        ),
      ),
    );
  }
}
