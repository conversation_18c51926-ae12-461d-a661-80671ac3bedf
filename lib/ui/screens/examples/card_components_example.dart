import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// Example screen to demonstrate the card components
class CardComponentsExample extends StatefulWidget {
  const CardComponentsExample({super.key});

  @override
  State<CardComponentsExample> createState() => _CardComponentsExampleState();
}

class _CardComponentsExampleState extends State<CardComponentsExample> {
  bool _showCardDetails = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(
        headerText: 'Card Components',
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
        child: ListView(
          children: [
            const YBox(24),
            const CustomSubHeader(
              title: 'Card Components',
              subtitle: 'Reusable components for displaying card information',
            ),
            const YBox(24),

            // Example of CreditCardView
            Text(
              'CreditCardView',
              style: AppTypography.text16,
            ),
            const YBox(8),
            CreditCardView(
              cardNumber: '5399 8282 8282 8210',
              cardHolder: 'Adebayo Ko<PERSON>ole',
              expiryDate: '12/25',
              cvv: '123',
              backgroundImage: AppImages.card,
              cardTypeLogo: AppSvgs.visa,
              showDetails: _showCardDetails,
              onTap: () {
                setState(() {
                  _showCardDetails = !_showCardDetails;
                });
              },
            ),
            const YBox(8),
            Text(
              'Tap the card to ${_showCardDetails ? 'hide' : 'show'} details',
              style: AppTypography.text12,
              textAlign: TextAlign.center,
            ),

            const YBox(32),

            // Example of CardInfoField
            Text(
              'CardInfoField',
              style: AppTypography.text16,
            ),
            const YBox(16),
            Container(
              padding: EdgeInsets.all(Sizer.radius(16)),
              decoration: BoxDecoration(
                color: AppColors.primaryPurple,
                borderRadius: BorderRadius.circular(Sizer.radius(8)),
              ),
              child: const Column(
                children: [
                  CardInfoField(
                    label: 'Card Holder',
                    value: 'Adebayo Kolawole',
                  ),
                  YBox(16),
                  CardInfoField(
                    label: 'Card Number',
                    value: '5399 8282 8282 8210',
                  ),
                  YBox(16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CardInfoField(
                        label: 'Expiry Date',
                        value: '12/25',
                      ),
                      CardInfoField(
                        label: 'CVV',
                        value: '123',
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const YBox(32),

            // Example with custom colors
            Text(
              'Custom Styling',
              style: AppTypography.text16,
            ),
            const YBox(16),
            CreditCardView(
              cardNumber: '4111 1111 1111 1111',
              cardHolder: 'John Doe',
              expiryDate: '09/26',
              cvv: '456',
              backgroundColor: AppColors.pri400,
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Card tapped!'),
                  ),
                );
              },
            ),

            const YBox(100),
          ],
        ),
      ),
    );
  }
}
