import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// Example screen to demonstrate the SelectionModal with maxHeight
class SelectionModalExample extends StatefulWidget {
  const SelectionModalExample({super.key});

  @override
  State<SelectionModalExample> createState() => _SelectionModalExampleState();
}

class _SelectionModalExampleState extends State<SelectionModalExample> {
  // Sample data for the selection modal
  final List<String> _shortList = [
    'Option 1',
    'Option 2',
    'Option 3',
    'Option 4',
    'Option 5',
  ];

  final List<String> _longList = [
    'Option 1',
    'Option 2',
    'Option 3',
    'Option 4',
    'Option 5',
    'Option 6',
    'Option 7',
    'Option 8',
    'Option 9',
    'Option 10',
    'Option 11',
    'Option 12',
    'Option 13',
    'Option 14',
    'Option 15',
    'Option 16',
    'Option 17',
    'Option 18',
    'Option 19',
    'Option 20',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: AppBar(
        title: const Text('Selection Modal Example'),
        backgroundColor: AppColors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const CustomSubHeader(
              title: 'Selection Modal with Max Height',
              subtitle:
                  'Demonstrate how to limit the height of selection modals',
            ),
            const YBox(40),

            // Button to show modal with short list (no max height needed)
            CustomBtn.solid(
              text: 'Show Modal (Short List)',
              onTap: () {
                _showSelectionModal(context, _shortList, null);
              },
            ),

            const YBox(16),

            // Button to show modal with long list and max height
            CustomBtn.solid(
              text: 'Show Modal (Long List with Max Height)',
              onTap: () {
                _showSelectionModal(context, _longList, 400);
              },
            ),

            const YBox(16),

            // Button to show modal with long list and smaller max height
            CustomBtn.solid(
              text: 'Show Modal (Long List with Small Height)',
              onTap: () {
                _showSelectionModal(context, _longList, 300);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Show a selection modal with the given options and max height
  void _showSelectionModal(
      BuildContext context, List<String> options, double? maxHeight) async {
    final result = await showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => SelectionModal(
        title: 'Select an option:',
        selections: options,
        maxHeight: maxHeight,
        selectionColor: AppColors.primaryPurple,
      ),
    );

    if (result != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Selected: $result'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
