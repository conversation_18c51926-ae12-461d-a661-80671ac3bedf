import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// Example screen to demonstrate the NotificationTile with time ago formatting
class NotificationTimeExample extends StatelessWidget {
  const NotificationTimeExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample notifications with different timestamps
    final notifications = [
      {
        'title': 'Just now',
        'subtitle': 'This notification was just created',
        'createdAt': DateTime.now(),
      },
      {
        'title': 'Minutes ago',
        'subtitle': 'This notification was created minutes ago',
        'createdAt': DateTime.now().subtract(const Duration(minutes: 5)),
      },
      {
        'title': 'Hours ago',
        'subtitle': 'This notification was created hours ago',
        'createdAt': DateTime.now().subtract(const Duration(hours: 3)),
      },
      {
        'title': 'Yesterday',
        'subtitle': 'This notification was created yesterday',
        'createdAt': DateTime.now().subtract(const Duration(days: 1)),
      },
      {
        'title': 'Days ago',
        'subtitle': 'This notification was created a few days ago',
        'createdAt': DateTime.now().subtract(const Duration(days: 4)),
      },
      {
        'title': 'Last week',
        'subtitle': 'This notification was created last week',
        'createdAt': DateTime.now().subtract(const Duration(days: 6)),
      },
      {
        'title': 'Weeks ago',
        'subtitle': 'This notification was created weeks ago',
        'createdAt': DateTime.now().subtract(const Duration(days: 14)),
      },
      {
        'title': 'Last month',
        'subtitle': 'This notification was created last month',
        'createdAt': DateTime.now().subtract(const Duration(days: 25)),
      },
      {
        'title': 'Months ago',
        'subtitle': 'This notification was created months ago',
        'createdAt': DateTime.now().subtract(const Duration(days: 90)),
      },
      {
        'title': 'Last year',
        'subtitle': 'This notification was created last year',
        'createdAt': DateTime.now().subtract(const Duration(days: 365)),
      },
      {
        'title': 'Years ago',
        'subtitle': 'This notification was created years ago',
        'createdAt': DateTime.now().subtract(const Duration(days: 730)),
      },
    ];

    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: AppBar(
        title: const Text('Notification Time Example'),
        backgroundColor: AppColors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const CustomSubHeader(
              title: 'Time Ago Formatting',
              subtitle: 'Demonstration of time ago formatting in notifications',
            ),
            const YBox(16),
            
            // Information box
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.neutral300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Time Formatting:',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const YBox(8),
                  Text(
                    '• Less than a minute: "Just now"',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  Text(
                    '• Less than an hour: "X minutes ago"',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  Text(
                    '• Less than a day: "X hours ago"',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  Text(
                    '• Less than a week: "X days ago"',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  Text(
                    '• Less than a month: "X weeks ago"',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  Text(
                    '• Less than a year: "X months ago"',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  Text(
                    '• More than a year: "X years ago"',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                ],
              ),
            ),
            
            const YBox(16),
            
            // Notification list
            Expanded(
              child: ListView.separated(
                itemCount: notifications.length,
                separatorBuilder: (context, index) => const YBox(12),
                itemBuilder: (context, index) {
                  final notification = notifications[index];
                  return NotificationTile(
                    title: notification['title'] as String,
                    subtitle: notification['subtitle'] as String,
                    createdAt: notification['createdAt'] as DateTime,
                    onTap: () {
                      // Show the actual DateTime for comparison
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Actual time: ${notification['createdAt'].toString()}',
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
