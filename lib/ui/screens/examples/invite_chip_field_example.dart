import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// Example screen to demonstrate the InviteChipField widget
class InviteChipFieldExample extends StatefulWidget {
  const InviteChipFieldExample({super.key});

  @override
  State<InviteChipFieldExample> createState() => _InviteChipFieldExampleState();
}

class _InviteChipFieldExampleState extends State<InviteChipFieldExample> {
  List<String> _invitees = [];
  final _formKey = GlobalKey<FormState>();
  final _inviteFieldKey = GlobalKey<InviteChipFieldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: AppBar(
        title: const Text('Invite Chip Field Example'),
        backgroundColor: AppColors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.r),
        child: Form(
          key: _form<PERSON><PERSON>,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const CustomSubHeader(
                title: 'Invite Members',
                subtitle:
                    'Add email addresses or phone numbers to invite members',
              ),
              const YBox(24),

              // Our new InviteChipField widget with validation
              InviteChipField(
                key: _inviteFieldKey,
                labelText: 'Invite by phone number or email',
                hintText: 'Enter email or phone number',
                optionalText: ' (Required)',
                chipColor: Colors.grey.shade200,
                chipTextColor: Colors.black87,
                isRequired: true,
                onInviteesChanged: (invitees) {
                  setState(() {
                    _invitees = invitees;
                  });
                },
              ),

              const YBox(16),

              // Validation instructions
              Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: AppColors.neutral300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Validation Rules:',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const YBox(8),
                    Text(
                      '• Email must be in valid format (<EMAIL>)',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                    const YBox(4),
                    Text(
                      '• Phone number must have at least 11 digits',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                    const YBox(4),
                    Text(
                      '• Press space or comma after typing to add the entry',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                  ],
                ),
              ),

              const YBox(24),

              // Display the current invitees
              Text(
                'Current Invitees (${_invitees.length}):',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const YBox(8),
              ..._invitees.map(
                (invitee) => Padding(
                  padding: EdgeInsets.only(bottom: 4.h),
                  child: Text(
                    '• $invitee',
                    style: TextStyle(
                      fontSize: 14.sp,
                    ),
                  ),
                ),
              ),

              const Spacer(),
              CustomBtn.solid(
                text: 'Validate & Send Invites',
                onTap: () {
                  // Get the InviteChipField state and validate
                  final inviteFieldState = _inviteFieldKey.currentState;

                  if (inviteFieldState != null) {
                    if (!inviteFieldState.validate()) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please add at least one invitee'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                  }

                  // Show a dialog with the invitees
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Invites Ready to Send'),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('You are about to send invites to:'),
                          const YBox(8),
                          ..._invitees.map(
                            (invitee) => Padding(
                              padding: EdgeInsets.only(bottom: 4.h),
                              child: Text('• $invitee'),
                            ),
                          ),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Invites sent to ${_invitees.length} people'),
                              ),
                            );
                          },
                          child: const Text('Send'),
                        ),
                      ],
                    ),
                  );
                },
              ),
              const YBox(24),
            ],
          ),
        ),
      ),
    );
  }
}
