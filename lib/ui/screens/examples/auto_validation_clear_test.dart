import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

/// Test screen to demonstrate automatic validation error clearing functionality
class AutoValidationClearTestScreen extends StatefulWidget {
  const AutoValidationClearTestScreen({super.key});

  @override
  State<AutoValidationClearTestScreen> createState() =>
      _AutoValidationClearTestScreenState();
}

class _AutoValidationClearTestScreenState
    extends State<AutoValidationClearTestScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _genderController = TextEditingController();

  final FocusNode _nameFocus = FocusNode();
  final FocusNode _emailFocus = FocusNode();
  final FocusNode _genderFocus = FocusNode();

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _genderController.dispose();
    _nameFocus.dispose();
    _emailFocus.dispose();
    _genderFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Auto Validation Clear Test'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const CustomSubHeader(
              title: 'Automatic Validation Error Clearing',
              subtitle:
                  'Test programmatic text setting without manual setState calls',
            ),
            const YBox(32),

            // Name field with required validation
            Text(
              'Name Field (Required)',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              controller: _nameController,
              focusNode: _nameFocus,
              labelText: 'Full Name',
              hintText: 'Enter your full name',
              showLabelHeader: true,
              validationRules: const [
                RequiredRule(fieldName: 'name'),
              ],
              enableRealTimeValidation: true,
            ),
            const YBox(24),

            // Email field with email validation
            Text(
              'Email Field (Required + Email Format)',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              controller: _emailController,
              focusNode: _emailFocus,
              labelText: 'Email Address',
              hintText: 'Enter your email',
              showLabelHeader: true,
              validationRules: ValidationRules.requiredEmail(),
              enableRealTimeValidation: true,
            ),
            const YBox(24),

            // Gender field (read-only, set programmatically)
            Text(
              'Gender Field (Required, Read-only)',
              style: AppTypography.text16.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(8),
            CustomTextField(
              controller: _genderController,
              focusNode: _genderFocus,
              labelText: 'Gender',
              hintText: 'Select your gender',
              showLabelHeader: true,
              isReadOnly: true,
              validationRules: const [
                RequiredRule(fieldName: 'gender'),
              ],
              enableRealTimeValidation: true,
              suffixIcon: const Icon(Icons.arrow_drop_down),
              onTap: () => _showGenderSelection(),
            ),
            const YBox(32),

            // Test buttons
            Row(
              children: [
                Expanded(
                  child: CustomBtn.solid(
                    text: 'Set Sample Data',
                    onTap: _setSampleData,
                  ),
                ),
                const XBox(16),
                Expanded(
                  child: CustomBtn.solid(
                    text: 'Clear All',
                    isOutline: true,
                    onTap: _clearAllFields,
                  ),
                ),
              ],
            ),
            const YBox(16),

            // Instructions
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Test Instructions:',
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  const YBox(8),
                  Text(
                    '1. Leave fields empty and tap outside to trigger validation errors\n'
                    '2. Tap "Set Sample Data" to programmatically fill fields\n'
                    '3. Notice validation errors clear automatically without manual setState\n'
                    '4. Tap "Clear All" to reset and test again',
                    style: AppTypography.text12.copyWith(
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _setSampleData() {
    // Programmatically set text values - validation errors should clear automatically
    _nameController.text = 'John Doe';
    _emailController.text = '<EMAIL>';
    _genderController.text = 'Male';

    // No setState() call needed - CustomTextField handles this automatically!
  }

  void _clearAllFields() {
    _nameController.clear();
    _emailController.clear();
    _genderController.clear();
  }

  void _showGenderSelection() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(24.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Gender',
              style: AppTypography.text18.copyWith(fontWeight: FontWeight.bold),
            ),
            const YBox(24),
            ...['Male', 'Female', 'Other'].map(
              (gender) => ListTile(
                title: Text(gender),
                onTap: () {
                  _genderController.text = gender;
                  // No setState() call needed!
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
