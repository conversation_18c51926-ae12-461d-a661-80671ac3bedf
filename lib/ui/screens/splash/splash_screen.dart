// ignore_for_file: use_build_context_synchronously

import 'package:equalcash/core/core.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
    // FCMTokenService().getToken();
    // HeaderService().getDeviceInfo();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    _fadeAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    _animationController.forward();

    Future.delayed(const Duration(seconds: 4), () async {
      final accessToken = await StorageService.getAccessToken();
      NavigationHelper.navigateTo(
        routeName: accessToken != null
            ? RoutePath.loginScreen
            : RoutePath.onboardingScreen,
        replace: true,
      );
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.black,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Stack(
          children: [
            Container(
              width: Sizer.screenWidth,
              height: Sizer.screenHeight,
              decoration: const BoxDecoration(
                  gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppColors.purple89,
                  AppColors.primaryPurple,
                ],
              )),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  imageHelper(
                    AppImages.logo,
                    height: Sizer.height(55),
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 50,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(16),
                ),
                child: Text(
                  'A combined peer-to-peer banking and peer-to-peer lending.',
                  textAlign: TextAlign.center,
                  style: AppTypography.text18.copyWith(
                    color: AppColors.white,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
