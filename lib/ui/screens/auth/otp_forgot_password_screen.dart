import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class OtpForgotPasswordScreen extends StatefulWidget {
  const OtpForgotPasswordScreen({
    super.key,
    required this.phone,
  });

  final String phone;

  @override
  State<OtpForgotPasswordScreen> createState() =>
      _OtpForgotPasswordScreenState();
}

class _OtpForgotPasswordScreenState extends State<OtpForgotPasswordScreen> {
  TextEditingController pinC = TextEditingController();
  FocusNode pinF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinF);
    pinF.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    pinC.dispose();
    pinF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        return Scaffold(
          backgroundColor: AppColors.purpleF1,
          appBar: const CustomHeader(),
          body: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(10),
              const CustomSubHeader(
                title: 'Forgot Password',
                subtitle:
                    'Please enter a 6 digit code sent to your \nphone number',
              ),
              const YBox(64),
              Text(
                'Enter Code',
                style: TextStyle(
                  color: AppColors.neutral400,
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                ),
              ),
              const YBox(4),
              Pinput(
                defaultPinTheme: PinInputTheme.defaultPinTheme(),
                followingPinTheme: PinInputTheme.followPinTheme(),
                length: 6,
                controller: pinC,
                focusNode: pinF,
                showCursor: true,
                // onChanged: (value) => vm..reBuildUI(),
                onCompleted: (pin) {
                  // _verifyOtp();
                },
              ),
              const YBox(32),
              ResendCode(
                onResendCode: () {},
              ),
              const YBox(210),
              CustomBtn.solid(
                text: 'Verify OTP',
                onTap: () {
                  Navigator.pushNamed(
                      context, RoutePath.setNewForgotPasswordScreen);
                },
              ),
              const YBox(40),
            ],
          ),
        );
      },
    );
  }
}
