import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return BusyOverlay(
      show: ref.watch(profileVm).isBusy,
      child: Scaffold(
        backgroundColor: AppColors.purpleF1,
        appBar: const CustomHeader(),
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          children: [
            const YBox(20),
            const CustomSubHeader(
              title: 'Good Day',
              subtitle:
                  "Welcome back! Please login into your \nEqualcash account to continue",
            ),
            const YBox(80),
            CustomPinInput(
              onCompleted: (pin) async {
                printty('Entered PIN: $pin');
                final r = await ref.read(profileVm).confirmPin(pin.trim());

                handleApiResponse(
                  response: r,
                  duration: 2,
                  onCompleted: () {
                    Navigator.pushNamedAndRemoveUntil(
                      context,
                      RoutePath.dashboardNavigationScreen,
                      (route) => false,
                    );
                  },
                );
              },
            ),
            const YBox(12),
            Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () {},
                child: Text(
                  'Forgot Pin?',
                  style: AppTypography.text16.copyWith(
                    color: AppColors.primaryPurple,
                    height: 1.6,
                  ),
                ),
              ),
            ),
            const YBox(64),
            Align(
              alignment: Alignment.center,
              child: InkWell(
                onTap: () {},
                child: Text(
                  'Or please login using',
                  style: AppTypography.text16.copyWith(
                    color: AppColors.neutral400,
                  ),
                ),
              ),
            ),
            const YBox(12),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
                vertical: Sizer.height(12),
              ),
              decoration: const BoxDecoration(
                color: AppColors.primaryPurple,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  LoginRowIcon(
                    text: 'Face ID',
                    svgPath: AppSvgs.faceId,
                    onTap: _authenticateWithBiometric,
                  ),
                  svgHelper(AppSvgs.vLine),
                  LoginRowIcon(
                    text: 'Password',
                    svgPath: AppSvgs.key,
                    onTap: () {
                      Navigator.pushNamed(
                          context, RoutePath.passwordLoginScreen);
                    },
                  )
                ],
              ),
            ),
            const YBox(32),
            Align(
              alignment: Alignment.center,
              child: Text(
                'Don\'t have an account?',
                style: AppTypography.text16.copyWith(
                  color: AppColors.neutral200,
                ),
              ),
            ),
            const YBox(12),
            Align(
              alignment: Alignment.center,
              child: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.createAccountScreen);
                },
                child: Text(
                  'Create Account',
                  style: AppTypography.text16.copyWith(
                    color: AppColors.primaryPurple,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const YBox(40),
          ],
        ),
      ),
    );
  }

  _authenticateWithBiometric() async {
    final fingerPrintIsEnabled =
        await StorageService.getBoolItem(StorageKey.fingerPrintIsEnabled) ??
            false;

    if (!fingerPrintIsEnabled) {
      Navigator.pushNamed(
        context,
        RoutePath.enableFaceIdScreen,
        arguments: BiometricArg(fromLogin: true),
      );
    } else {
      final isAuthenticated = await BiometricService.authenticate();
      isAuthenticated
          ? Navigator.pushNamed(context, RoutePath.dashboardNavigationScreen)
          : FlushBarToast.fLSnackBar(
              message: "Biometric Authentication Failed",
            );
    }
  }
}
