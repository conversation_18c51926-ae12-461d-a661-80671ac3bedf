import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class LockedScreen extends StatelessWidget {
  const LockedScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        child: Column(
          children: [
            const YBox(160),
            svg<PERSON>elper(
              AppSvgs.lock,
              height: Sizer.height(56),
            ),
            const YBox(16),
            Text(
              'EqualCash Locked',
              style: AppTypography.text20.copyWith(
                color: AppColors.neutral400,
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(8),
            Text(
              'Unlock with face ID to open Equalcash',
              textAlign: TextAlign.center,
              style: AppTypography.text16.copyWith(
                color: AppColors.neutral200,
              ),
            ),
            const YBox(50),
            Container(
              padding: EdgeInsets.symmetric(
                vertical: Sizer.height(24),
                horizontal: Sizer.width(32),
              ),
              decoration: BoxDecoration(
                color: AppColors.pri300,
                borderRadius: BorderRadius.circular(Sizer.radius(16)),
              ),
              child: Column(
                children: [
                  svgHelper(AppSvgs.faceId),
                  const YBox(16),
                  Text(
                    'Face ID',
                    style: AppTypography.text16.copyWith(
                      color: AppColors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            const YBox(150),
            CustomBtn.solid(
              text: 'Use Face ID',
              onTap: () {},
            ),
            const YBox(24),
          ],
        ),
      ),
    );
  }
}
