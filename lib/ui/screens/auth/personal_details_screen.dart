import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class PersonalDetailsScreen extends StatefulWidget {
  const PersonalDetailsScreen({super.key});

  @override
  State<PersonalDetailsScreen> createState() => _PersonalDetailsScreenState();
}

class _PersonalDetailsScreenState extends State<PersonalDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      final createAcctVm = ref.watch(createAccountVm);
      return BusyOverlay(
        show: createAcctVm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.purpleF1,
          appBar: const CustomHeader(),
          body: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(10),
              const CustomSubHeader(
                title: 'Personal Details',
                subtitle: "Please enter the information below to continue",
              ),
              const YBox(40),
              CustomTextField(
                  controller: createAcctVm.firstNameC,
                  focusNode: createAcctVm.firstNameF,
                  labelText: 'First Name',
                  showLabelHeader: true,
                  onChanged: (p0) => createAcctVm.reBuildUI(),
                  onSubmitted: (p0) {
                    createAcctVm.firstNameF.unfocus();
                    createAcctVm.lastNameF.requestFocus();
                  }),
              const YBox(20),
              CustomTextField(
                  controller: createAcctVm.lastNameC,
                  focusNode: createAcctVm.lastNameF,
                  labelText: 'Last Name',
                  showLabelHeader: true,
                  onChanged: (p0) => createAcctVm.reBuildUI(),
                  onSubmitted: (p0) {
                    createAcctVm.firstNameF.unfocus();
                    createAcctVm.lastNameF.requestFocus();
                  }),
              const YBox(20),
              CustomTextField(
                  controller: createAcctVm.emailC,
                  focusNode: createAcctVm.emailF,
                  labelText: 'Email',
                  showLabelHeader: true,
                  onChanged: (p0) => createAcctVm.reBuildUI(),
                  onSubmitted: (p0) {
                    createAcctVm.firstNameF.unfocus();
                    createAcctVm.lastNameF.requestFocus();
                  }),
              const YBox(20),
              CustomTextField(
                  controller: createAcctVm.passwordC,
                  focusNode: createAcctVm.passwordF,
                  labelText: 'Password',
                  showLabelHeader: true,
                  isPassword: true,
                  onChanged: (p0) => createAcctVm.reBuildUI(),
                  onSubmitted: (p0) {
                    createAcctVm.firstNameF.unfocus();
                  }),
              const YBox(8),
              Text(
                  'Password must contain at least 8 characters, one upper case, a number & one special character',
                  style: AppTypography.text12.copyWith(
                    color: AppColors.neutral200,
                  )),
              const YBox(60),
              CustomBtn.solid(
                text: 'Continue',
                online: createAcctVm.personalDBtnActive,
                onTap: () async {
                  // Navigator.pushNamed(context, RoutePath.setPasscodeScreen);
                  FocusManager.instance.primaryFocus?.unfocus();
                  final createRef = ref.read(createAccountVm);
                  final r = await createRef.createAccount();
                  handleApiResponse(
                    response: r,
                    onCompleted: () {
                      Navigator.pushNamed(context, RoutePath.setPasscodeScreen);
                    },
                  );
                },
              ),
              const YBox(60),
            ],
          ),
        ),
      );
    });
  }
}
