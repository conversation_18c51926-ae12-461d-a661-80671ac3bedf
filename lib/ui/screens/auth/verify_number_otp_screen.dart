import 'package:equalcash/lib.dart';
import 'package:pinput/pinput.dart';

class VerifyNumberOtpScreen extends ConsumerStatefulWidget {
  const VerifyNumberOtpScreen({super.key});

  @override
  ConsumerState<VerifyNumberOtpScreen> createState() =>
      _VerifyNumberOtpScreenState();
}

class _VerifyNumberOtpScreenState extends ConsumerState<VerifyNumberOtpScreen> {
  TextEditingController pinC = TextEditingController();
  FocusNode pinF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinF);
    pinF.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(createAccountVm).requestOTP();
    });
  }

  @override
  void dispose() {
    pinC.dispose();
    pinF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final createAcctVm = ref.watch(createAccountVm);
    return BusyOverlay(
      show: createAcctVm.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.purpleF1,
        appBar: const CustomHeader(),
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          children: [
            const YBox(10),
            const CustomSubHeader(
              title: 'Verify your number',
              subtitle:
                  'Please enter a 6 digit code sent to your \nphone number',
            ),
            const YBox(60),
            Text(
              'Enter Code',
              style: TextStyle(
                color: AppColors.neutral400,
                fontSize: Sizer.text(16),
                fontWeight: FontWeight.w400,
              ),
            ),
            const YBox(4),
            Pinput(
              defaultPinTheme: PinInputTheme.defaultPinTheme(),
              followingPinTheme: PinInputTheme.followPinTheme(),
              length: 6,
              controller: pinC,
              focusNode: pinF,
              showCursor: true,
              onChanged: (pin) {
                if (pin.length == 6) {
                  _verifyOtp();
                }
              },
            ),
            const YBox(30),
            ResendCode(
              onResendCode: () {},
            ),
            const YBox(210),
            CustomBtn.solid(
              text: 'Verify OTP',
              onTap: () {
                _verifyOtp();
              },
            ),
            const YBox(40),
          ],
        ),
      ),
    );
  }

  _verifyOtp() async {
    final vm = ref.read(createAccountVm);
    final res = await vm.verifyOTP(otp: pinC.text.trim());
    handleApiResponse(
      response: res,
      onCompleted: () {
        Navigator.pushNamed(context, RoutePath.knowYouBetterScreen);
      },
    );
  }
}
