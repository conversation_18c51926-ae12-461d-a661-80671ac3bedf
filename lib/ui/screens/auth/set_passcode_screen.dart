import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class SetPasscodeScreen extends StatefulWidget {
  const SetPasscodeScreen({super.key});

  @override
  State<SetPasscodeScreen> createState() => _SetPasscodeScreenState();
}

class _SetPasscodeScreenState extends State<SetPasscodeScreen> {
  final pinC = TextEditingController();
  final pinF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinF);
    pinF.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    pinC.dispose();
    pinF.dispose();

    super.dispose();
  }

  bool get btnIsActive => pinC.text.length == 4;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(20),
        ),
        children: [
          const YBox(160),
          CustomPinWidget(
            controller: pinC,
            pinFocusNode: pinF,
            title: 'Choose a 4-digit passcode',
            subtitle: 'Set a 4 digit pin to make your wallet more secure',
            onChanged: (v) => setState(() {}),
          ),
          const YBox(210),
          CustomBtn.solid(
            text: 'Set Passcode',
            online: btnIsActive,
            onTap: () {
              FocusScope.of(context).unfocus();
              Navigator.pushNamed(
                context,
                RoutePath.repeatPasscodeScreen,
                arguments: pinC.text.trim(),
              );
            },
          ),
          const YBox(40),
        ],
      ),
    );
  }
}
