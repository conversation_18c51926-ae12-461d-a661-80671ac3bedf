import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class AccountSuccessScreen extends StatelessWidget {
  const AccountSuccessScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        child: Column(
          children: [
            const YBox(200),
            image<PERSON><PERSON><PERSON>(
              AppImages.accountCreated,
              height: Sizer.height(96),
            ),
            const YBox(24),
            Text(
              'Account Created Successfully',
              style: AppTypography.text20.copyWith(
                color: AppColors.neutral400,
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(8),
            Text(
              'Congratulations! Your account has been \nsuccessfully created!',
              textAlign: TextAlign.center,
              style: AppTypography.text16.copyWith(
                color: AppColors.neutral200,
              ),
            ),
            const YBox(150),
            CustomBtn.solid(
              text: 'Proceed To Dashboard',
              onTap: () {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  RoutePath.completeYourProfileScreen,
                  (route) => false,
                );
              },
            ),
            const YBox(24),
          ],
        ),
      ),
    );
  }
}
