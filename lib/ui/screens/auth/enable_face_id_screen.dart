import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class EnableFaceIdScreen extends StatelessWidget {
  const EnableFaceIdScreen({
    super.key,
    this.args,
  });

  final BiometricArg? args;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(200),
            imageHelper(
              AppImages.solarFace,
              height: Sizer.height(96),
            ),
            const YBox(24),
            Text(
              'Log in faster with \nface ID',
              style: AppTypography.text30.copyWith(
                color: AppColors.neutral400,
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(8),
            Text(
              'Use your face ID for quick and secure \naccess to your account',
              style: AppTypography.text16.copyWith(
                color: AppColors.neutral200,
              ),
            ),
            const YBox(50),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
                vertical: Sizer.height(12),
              ),
              decoration: BoxDecoration(
                  color: AppColors.secondary100,
                  borderRadius: BorderRadius.circular(Sizer.radius(12))),
              child: Text(
                'Don’t enable face ID if you share your phone with someone else',
                style: AppTypography.text12.copyWith(
                  color: AppColors.neutral300,
                  height: 1.6,
                ),
              ),
            ),
            const YBox(24),
            CustomBtn.solid(
              text: 'Enable Face ID',
              onTap: () async {
                // Navigator.pushNamed(context, RoutePath.personalDetailsScreen);
                // ModalWrapper.showAlertDialog(
                //   context,
                //   content: 'Do you want to allow Equalcash to use Face ID?',
                //   leftBtnText: 'Don’t Allow',
                //   rightBtnText: 'Allow',
                //   contentTextStyle: AppTypography.text14.copyWith(
                //     fontWeight: FontWeight.w400,
                //     color: AppColors.neutral300,
                //   ),
                //   rightBtnOnTap: () {
                //     Navigator.pop(context);
                //     Navigator.pushNamed(
                //         context, RoutePath.allowNotificationScreen);
                //   },
                // );

                await BiometricService.authenticate().then((value) {
                  if (value) {
                    StorageService.storeBoolItem(
                        StorageKey.fingerPrintIsEnabled, value);
                    if (args?.fromLogin == true) {
                      Navigator.pop(context);
                      return;
                    }
                    Navigator.pushNamed(
                        context, RoutePath.allowNotificationScreen);
                  } else {
                    FlushBarToast.fLSnackBar(
                      message: "Biometric Authentication Failed",
                    );
                  }
                });
              },
            ),
            const YBox(24),
            SkipForNow(
              onTap: () {
                if (args?.fromLogin == true) {
                  Navigator.pop(context);
                  return;
                }
                Navigator.pushNamed(context, RoutePath.allowNotificationScreen);
              },
            ),
          ],
        ),
      ),
    );
  }
}
