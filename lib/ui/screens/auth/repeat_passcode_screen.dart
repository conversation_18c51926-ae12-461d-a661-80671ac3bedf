import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class RepeatPasscodeScreen extends StatefulWidget {
  const RepeatPasscodeScreen({
    super.key,
    required this.passcode,
  });

  final String passcode;

  @override
  State<RepeatPasscodeScreen> createState() => _RepeatPasscodeScreenState();
}

class _RepeatPasscodeScreenState extends State<RepeatPasscodeScreen> {
  final pinConfirmC = TextEditingController();
  final pinConfirmF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinConfirmF);
    pinConfirmF.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    pinConfirmC.dispose();
    pinConfirmF.dispose();

    super.dispose();
  }

  bool get btnIsActive => pinConfirmC.text.length == 4;

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        return BusyOverlay(
          show: ref.watch(profileVm).isBusy,
          child: Scaffold(
            backgroundColor: AppColors.purpleF1,
            body: ListView(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(20),
              ),
              children: [
                const YBox(160),
                CustomPinWidget(
                  controller: pinConfirmC,
                  pinFocusNode: pinConfirmF,
                  title: 'Please repeat the passcode',
                  subtitle: 'Set a 4 digit pin to make your wallet more secure',
                  onChanged: (p0) => ref.read(profileVm).reBuildUI(),
                ),
                const YBox(210),
                CustomBtn.solid(
                  text: 'Set Passcode',
                  online: btnIsActive,
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    final profileRef = ref.read(profileVm);
                    final r = await profileRef.createPin(
                      pin: widget.passcode,
                      pinConfirm: pinConfirmC.text,
                    );
                    handleApiResponse(
                      response: r,
                      onCompleted: () {
                        Navigator.pushNamed(
                            context, RoutePath.enableFaceIdScreen);
                      },
                    );
                  },
                ),
                const YBox(40),
              ],
            ),
          ),
        );
      },
    );
  }
}
