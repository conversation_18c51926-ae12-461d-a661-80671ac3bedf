import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class SetNewForgotPasswordScreen extends StatefulWidget {
  const SetNewForgotPasswordScreen({super.key});

  @override
  State<SetNewForgotPasswordScreen> createState() =>
      _SetNewForgotPasswordScreenState();
}

class _SetNewForgotPasswordScreenState
    extends State<SetNewForgotPasswordScreen> {
  TextEditingController newPasswordC = TextEditingController();
  TextEditingController confirmPasswordC = TextEditingController();
  FocusNode newPasswordF = FocusNode();
  FocusNode confirmPasswordF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    confirmPasswordC.dispose();
    newPasswordC.dispose();
    newPasswordF.dispose();
    confirmPasswordF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Forgot Password',
            subtitle: "Create a new password to continue",
          ),
          const YBox(80),
          CustomTextField(
            controller: newPasswordC,
            focusNode: newPasswordF,
            labelText: 'New Password',
            showLabelHeader: true,
            isPassword: true,
          ),
          const YBox(4),
          Text(
            'Password must contain at least 8 characters, one upper case, a number & one special character',
            style: AppTypography.text12.copyWith(color: AppColors.neutral200),
          ),
          const YBox(30),
          CustomTextField(
            controller: confirmPasswordC,
            focusNode: confirmPasswordF,
            labelText: 'Password',
            showLabelHeader: true,
            isPassword: true,
          ),
          const YBox(4),
          Text(
            'Password must contain at least 8 characters, one upper case, a number & one special character',
            style: AppTypography.text12.copyWith(color: AppColors.neutral200),
          ),
          const YBox(60),
          CustomBtn.solid(
            text: 'Confirm Password',
            onTap: () {
              Navigator.pushNamed(context, RoutePath.lockedScreen);
            },
          ),
        ],
      ),
    );
  }
}
