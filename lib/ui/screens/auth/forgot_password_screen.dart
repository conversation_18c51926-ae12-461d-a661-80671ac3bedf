import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final countryPicker = const FlCountryCodePicker(
    title: SizedBox.shrink(),
  );
  TextEditingController dialCodeC = TextEditingController();
  TextEditingController phoneC = TextEditingController();
  FocusNode phoneF = FocusNode();

  CountryCode selectedCountry =
      const CountryCode(code: 'NG', name: 'Nigeria', dialCode: "+234");

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, phoneF);
    phoneF.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setSelectedCountry(selectedCountry);
    });
  }

  bool get isValid => phoneC.text.length == 10;
  bool get isFormValid => isValid && dialCodeC.text.isNotEmpty;

  @override
  void dispose() {
    phoneC.dispose();
    phoneF.dispose();

    super.dispose();
  }

  setSelectedCountry(CountryCode country) {
    selectedCountry = country;
    dialCodeC.text = country.dialCode;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        return BusyOverlay(
          child: Scaffold(
            backgroundColor: AppColors.purpleF1,
            appBar: const CustomHeader(),
            body: ListView(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              children: [
                const YBox(20),
                const CustomSubHeader(
                  title: 'Forgot Password',
                  subtitle:
                      "Please provide the phone number you used \nto create your account to receive an \nidentification code.",
                ),
                const YBox(80),
                Text(
                  'Phone Number',
                  style: TextStyle(
                    color: AppColors.neutral400,
                    fontSize: Sizer.text(16),
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const YBox(4),
                Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: CustomTextField(
                        controller: dialCodeC,
                        isReadOnly: true,
                        onTap: () async {
                          final CountryCode? country =
                              await countryPicker.showPicker(
                            context: context,
                            pickerMaxHeight: Sizer.screenHeight * 0.8,
                          );

                          if (country != null) {
                            selectedCountry = country;
                            dialCodeC.text = country.dialCode;
                            setState(() {});
                          }
                        },
                      ),
                    ),
                    const XBox(10),
                    Expanded(
                      flex: 8,
                      child: CustomTextField(
                        controller: phoneC,
                        focusNode: phoneF,
                        hintText: 'Phone number',
                        keyboardType: KeyboardType.number,
                      ),
                    ),
                  ],
                ),
                const YBox(140),
                CustomBtn.solid(
                  text: 'Continue',
                  online: isFormValid,
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    final r = await ref.read(passwordVm).pwdRequestOtp(
                          recipient: '${dialCodeC.text}${phoneC.text}',
                        );
                    handleApiResponse(
                      response: r,
                      onCompleted: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.otpForgotPasswordScreen,
                          arguments: '${dialCodeC.text}${phoneC.text}',
                        );
                      },
                    );
                  },
                ),
                const YBox(100),
              ],
            ),
          ),
        );
      },
    );
  }
}
