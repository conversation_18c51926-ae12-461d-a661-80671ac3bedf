import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class KnowYouBetterScreen extends StatelessWidget {
  const KnowYouBetterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(200),
            image<PERSON><PERSON>per(
              AppImages.emojiPerson,
              height: Sizer.height(96),
            ),
            const YBox(24),
            Text(
              'Now let’s get to know \nyou better',
              style: AppTypography.text30.copyWith(
                color: AppColors.neutral400,
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(8),
            Text(
              'We want to always improve your \nexperience, so we will like to know you \nmore better',
              style: AppTypography.text16.copyWith(
                color: AppColors.neutral200,
              ),
            ),
            const YBox(130),
            CustomBtn.solid(
              text: 'Continue',
              onTap: () {
                Navigator.pushNamed(context, RoutePath.personalDetailsScreen);
              },
            ),
          ],
        ),
      ),
    );
  }
}
