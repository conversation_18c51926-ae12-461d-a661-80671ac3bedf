import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class AllowNotificationScreen extends StatelessWidget {
  const AllowNotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(200),
            imageHelper(
              AppImages.bellEmoji,
              height: Sizer.height(96),
            ),
            const YBox(24),
            Text(
              'Get an instant \nnotification from us',
              style: AppTypography.text30.copyWith(
                color: AppColors.neutral400,
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(8),
            Text(
              'Turn on your notifications so we can \nupdate you on recent activities',
              style: AppTypography.text16.copyWith(
                color: AppColors.neutral200,
              ),
            ),
            const YBox(150),
            CustomBtn.solid(
              text: 'Allow notifications',
              onTap: () {
                // Navigator.pushNamed(context, RoutePath.personalDetailsScreen);
                ModalWrapper.showAlertDialog(
                  context,
                  content: 'Allow notifications from \nEqualcash ',
                  leftBtnText: 'Don’t Allow',
                  rightBtnText: 'Allow',
                  contentTextStyle: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w400,
                    color: AppColors.neutral300,
                  ),
                  rightBtnOnTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(
                        context, RoutePath.accountSuccessScreen);
                  },
                );
              },
            ),
            const YBox(24),
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, RoutePath.accountSuccessScreen);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Maybe Later',
                    style: AppTypography.text16.copyWith(
                      color: AppColors.pri300,
                    ),
                  ),
                  const XBox(8),
                  svgHelper(AppSvgs.skipArrowRight),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
