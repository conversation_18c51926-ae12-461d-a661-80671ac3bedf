name: equalcash
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.11
  flutter_screenutil: ^5.9.3
  intl: ^0.19.0
  another_flushbar: ^1.12.30
  equatable: ^2.0.5
  skeletonizer: ^1.4.2
  dio: ^5.1.1
  pretty_dio_logger: ^1.4.0
  pinput: ^5.0.0
  cached_network_image: ^3.4.1
  flutter_dotenv: ^5.1.0
  iconsax: ^0.0.8
  # firebase_messaging: ^15.1.6
  # firebase_core: ^3.9.0
  currency_text_input_formatter: ^2.2.6
  path_provider: ^2.1.5
  package_info_plus: ^8.1.2
  device_info_plus: ^11.2.0
  # device_information: ^0.0.4
  permission_handler: ^11.3.1
  collection: ^1.18.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  fl_country_code_picker: ^0.1.9+1
  flutter_riverpod: ^2.6.1
  local_auth: ^2.3.0
  loading_animation_widget: ^1.3.0
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  webview_flutter: ^4.11.0
  page_transition: ^2.2.1
  image_picker: ^1.1.2
  image_cropper: ^9.1.0
  rename_app: ^1.6.3
  # flutter_spinkit: ^5.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  riverpod_lint: ^2.6.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgs/
    - assets/svgs/socials/
    - .env
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Nohemi
      fonts:
        - asset: assets/fonts/Nohemi/Nohemi-ExtraLight-BF6438cc58a2634.ttf
          weight: 200
        - asset: assets/fonts/Nohemi/Nohemi-Regular-BF6438cc4d0e493.ttf
          weight: 300
        - asset: assets/fonts/Nohemi/Nohemi-Regular-BF6438cc4d0e493.ttf
          weight: 400
        - asset: assets/fonts/Nohemi/Nohemi-Medium-BF6438cc5883899.ttf
          weight: 500
        - asset: assets/fonts/Nohemi/Nohemi-SemiBold-BF6438cc588a48a.ttf
          weight: 600
        - asset: assets/fonts/Nohemi/Nohemi-Bold-BF6438cc587b5b5.ttf
          weight: 700
        - asset: assets/fonts/Nohemi/Nohemi-ExtraBold-BF6438cc5881baf.ttf
          weight: 800
        - asset: assets/fonts/Nohemi/Nohemi-Black-BF6438cc58744d4.ttf
          weight: 900

  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
